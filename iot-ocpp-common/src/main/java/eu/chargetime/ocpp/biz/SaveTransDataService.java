package eu.chargetime.ocpp.biz;

import com.cdz360.base.utils.StringUtils;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Timer;
import java.util.TimerTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SaveTransDataService {

    private static final Logger logger = LoggerFactory.getLogger(SaveTransDataService.class);

    private static Timer timer = new Timer();

    /**
     * 写入文件
     *
     * @param msg 需要带上 [桩/云] [上行/下行]
     */
    public static synchronized void write(String traceId, String msg) {

        //logger.info("文件写入。traceId: {}, msg:{}", traceId, msg);

        synchronized (SaveTransDataService.timer) {
            timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    String pre = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS").format(new Date());
                    try {
                        TransDataFile.write(pre + msg);
                    } catch (IOException e) {
                        // write to file error
                        logger.error("[{}] write to file error: {}.", traceId, e.getMessage(), e);

                        // 重新写入
                        TransDataFile.init();
                    }
                }
            }, 0);
        }
    }

    /**
     * 桩下行
     *
     * @param msg 无需要带上 [桩/云] [上行/下行]
     */
    public static synchronized void evseImport(String traceId, String peerAddr, String evseNo,
        String msg) {
        StringBuilder str = new StringBuilder(" [下] [");
        str.append(evseNo).append("] [");
        if (StringUtils.isNotBlank(peerAddr)) {
            str.append(peerAddr.replaceFirst("/", ""));
        } else {
            str.append(peerAddr);
        }
        str.append("] ").append(msg);
        SaveTransDataService.write(traceId, str.toString());
    }

    /**
     * 桩上行
     *
     * @param msg 无需要带上 [桩/云] [上行/下行]
     */
    public static synchronized void evseExport(String traceId, String peerAddr, String evseNo,
        String msg) {
        StringBuilder str = new StringBuilder(" [上] [");
        str.append(evseNo).append("] [");
        if (StringUtils.isNotBlank(peerAddr)) {
            str.append(peerAddr.replaceFirst("/", ""));
        } else {
            str.append(peerAddr);
        }
        str.append("] ").append(msg);
        SaveTransDataService.write(traceId, str.toString());
    }
}
