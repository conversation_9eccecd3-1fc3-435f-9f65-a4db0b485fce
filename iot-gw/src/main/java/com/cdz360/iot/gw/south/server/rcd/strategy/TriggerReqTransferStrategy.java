package com.cdz360.iot.gw.south.server.rcd.strategy;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.model.evse.rcd.RcdBaseConfVo;
import com.cdz360.iot.gw.model.type.EvseBrand;
import com.cdz360.iot.gw.south.server.JsonServerImpl;
import eu.chargetime.ocpp.model.core.DataTransferConfirmation;
import eu.chargetime.ocpp.model.core.DataTransferRequest;
import eu.chargetime.ocpp.model.core.DataTransferStatus;
import eu.chargetime.ocpp.model.dc.type.DataTransferMessage;
import jakarta.annotation.PostConstruct;
import java.time.Duration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TriggerReqTransferStrategy implements IDataTransferStrategy {

    @Autowired
    private DataTransferStrategyFactory strategyFactory;

    @Autowired
    private EvseRepository evseRepository;

    @Autowired
    private JsonServerImpl jsonServer;

    @PostConstruct
    public void init() {
        strategyFactory.addStrategy(DataTransferMessage.MESSAGE_TRIGGER_REQ, this);
    }

    @Override
    public DataTransferConfirmation dataProcessing(DataTransferRequest request) {
        String tid = request.getBase().getTid();
        String evseNo = request.getBase().getEvseNo();
        log.info("[{} {}] {} dataProcessing. vendorId: {}", tid, evseNo, request.getMessageId(),
            request.getVendorId());

        DataTransferConfirmation confirmation = new DataTransferConfirmation(
            DataTransferStatus.Accepted);
        confirmation.setBase(request.getBase());

        RcdBaseConfVo confVo = new RcdBaseConfVo();
        confVo.setResult(IotGwConstants.SUCCESS);
        confVo.setMessageId(DataTransferMessage.MESSAGE_TRIGGER_CONF.getStr());
        confirmation.setData(JsonUtils.toJsonString(confVo));

        if (EvseBrand.RCD.getVendorId().equals(request.getVendorId())) {
            Mono.delay(Duration.ofSeconds(1))
                .doOnNext(e -> {
                    jsonServer.sendRcdEvsePriceByConditions(request.getBase(), null);
                })
                .subscribe();
        } else {
            log.info("[{} {}] {} 无需处理. vendorId: {}", tid, evseNo, request.getMessageId(),
                request.getVendorId());
        }

        return confirmation;
    }

}
