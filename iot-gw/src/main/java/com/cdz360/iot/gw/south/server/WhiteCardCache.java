package com.cdz360.iot.gw.south.server;

import com.cdz360.iot.gw.config.IotConfigProperties;
import com.cdz360.iot.gw.north.model.WhiteCard;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WhiteCardCache {

    /**
     * TODO: 2025/4/15 WZFIX 白名单管理方案选型：
     * 单节点小规模部署 ：当前文件缓存方案足够，但如果预计会有以下情况，建议升级到数据库或Redis方案：
     * - 充电桩数量增长（>1000台）
     * - 需要历史记录查询功能
     * - 需要多服务实例共享缓存
     * - 对数据可靠性要求极高
     */
    private static final String CACHE_FILE_PATH = "whitelistCache.json";
    private static final int MAX_VERSIONS_PER_EVSE = 5; // 每个充电桩最多保留的版本数

    // 缓存结构：Map<evseNo, Map<listVersion, List<WhiteCard>>>
    private final ConcurrentHashMap<String, LinkedHashMap<Integer, List<WhiteCard>>> whiteCardCache = new ConcurrentHashMap<>();

    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final ObjectMapper objectMapper = new ObjectMapper(); // 新增ObjectMapper
    @Autowired
    private IotConfigProperties configProperties;
    private volatile boolean isChange = false;

    @PostConstruct
    public void init() {
        loadFromFile();
        // 每5分钟检查一次是否需要保存文件
        scheduler.scheduleWithFixedDelay(this::saveIfNeeded, 5, 5, TimeUnit.MINUTES);
    }

    @PreDestroy
    public void destroy() {
        // 程序关闭前确保数据被保存
        saveIfNeeded();
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(1, TimeUnit.MINUTES)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    public void saveWhiteCard(String evseNo, Integer listVersion, List<WhiteCard> whiteCards) {
        whiteCardCache.compute(evseNo, (k, versionMap) -> {
            if (versionMap == null) {
                versionMap = new LinkedHashMap<>(MAX_VERSIONS_PER_EVSE, 0.75f, true) {
                    @Override
                    protected boolean removeEldestEntry(
                        Map.Entry<Integer, List<WhiteCard>> eldest) {
                        return size() > MAX_VERSIONS_PER_EVSE;
                    }
                };
            }
            versionMap.put(listVersion, whiteCards);
            return versionMap;
        });
        isChange = true;
    }

    private synchronized void saveIfNeeded() {
        if (isChange) {
            saveToFile();
            isChange = false;
        }
    }

    /**
     * 获取白名单卡列表
     */
    public List<WhiteCard> getWhiteCards(String evseNo, Integer listVersion) {
        Map<Integer, List<WhiteCard>> versionMap = whiteCardCache.get(evseNo);
        return versionMap != null ? versionMap.get(listVersion) : null;
    }

    /**
     * 获取充电桩的最新白名单版本
     */
    public Integer getLatestVersion(String evseNo) {
        Map<Integer, List<WhiteCard>> versionMap = whiteCardCache.get(evseNo);
        return versionMap != null ? versionMap.keySet().stream().max(Integer::compareTo)
            .orElse(null) : null;
    }

    /**
     * 检查充电桩是否存在指定版本的白名单
     */
    public boolean containsVersion(String evseNo, Integer listVersion) {
        Map<Integer, List<WhiteCard>> versionMap = whiteCardCache.get(evseNo);
        return versionMap != null && versionMap.containsKey(listVersion);
    }

    /**
     * 检查充电桩是否存在任何白名单
     */
    public boolean containsEvse(String evseNo) {
        return whiteCardCache.containsKey(evseNo);
    }

    /**
     * 将缓存保存到文件（流式写入，避免内存溢出）
     */
    private void saveToFile() {
        File file = new File(configProperties.getRtCfgPath(), CACHE_FILE_PATH);
        try (FileOutputStream fos = new FileOutputStream(
            file); JsonGenerator generator = new JsonFactory().createGenerator(fos)) {

            generator.writeStartObject(); // 开始整个JSON对象

            for (Map.Entry<String, LinkedHashMap<Integer, List<WhiteCard>>> entry : whiteCardCache.entrySet()) {
                String evseNo = entry.getKey();
                generator.writeFieldName(evseNo); // 写入充电桩编号作为字段名
                generator.writeStartObject(); // 开始充电桩的版本映射

                for (Map.Entry<Integer, List<WhiteCard>> versionEntry : entry.getValue()
                    .entrySet()) {
                    generator.writeFieldName(versionEntry.getKey().toString()); // 版本号作为字段名
                    generator.writeStartArray(); // 开始白名单卡数组

                    for (WhiteCard card : versionEntry.getValue()) {
                        generator.writeObject(card); // 写入单个白名单卡
                    }

                    generator.writeEndArray(); // 结束白名单卡数组
                }

                generator.writeEndObject(); // 结束充电桩的版本映射
            }

            generator.writeEndObject(); // 结束整个JSON对象
        } catch (IOException e) {
            log.error("保存白名单缓存到文件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从文件加载缓存（流式读取，避免内存溢出）
     */
    private void loadFromFile() {
        Path path = Paths.get(configProperties.getRtCfgPath(), CACHE_FILE_PATH);
        if (!Files.exists(path)) {
            log.info("白名单缓存文件不存在，无需从文件加载");
            return;
        }

        // 检查文件大小并记录日志，但不设置硬性限制
        try {
            long fileSize = Files.size(path);
            if (fileSize > 50 * 1024 * 1024) { // 50MB
                log.warn("白名单缓存文件较大: {}MB，可能影响加载性能", fileSize / (1024 * 1024));
            }
        } catch (IOException e) {
            log.warn("无法获取缓存文件大小: {}", e.getMessage());
        }

        try (FileInputStream fis = new FileInputStream(
            new File(configProperties.getRtCfgPath(), CACHE_FILE_PATH));
            // 用objectMapper创建parser，自动带ObjectCodec
            JsonParser parser = objectMapper.getFactory().createParser(fis)) {

            if (parser.nextToken() != JsonToken.START_OBJECT) {
                return;
            }

            while (parser.nextToken() != JsonToken.END_OBJECT) {
                String evseNo = parser.getCurrentName();
                parser.nextToken(); // 移动到充电桩对象开始

                LinkedHashMap<Integer, List<WhiteCard>> versionMap = new LinkedHashMap<>(
                    MAX_VERSIONS_PER_EVSE, 0.75f, true) {
                    @Override
                    protected boolean removeEldestEntry(
                        Map.Entry<Integer, List<WhiteCard>> eldest) {
                        return size() > MAX_VERSIONS_PER_EVSE;
                    }
                };

                while (parser.nextToken() != JsonToken.END_OBJECT) {
                    Integer listVersion = Integer.parseInt(parser.getCurrentName());
                    parser.nextToken(); // 移动到数组开始

                    List<WhiteCard> whiteCards = parser.readValueAs(
                        new TypeReference<List<WhiteCard>>() {
                        });
                    versionMap.put(listVersion, whiteCards);
                }

                whiteCardCache.put(evseNo, versionMap);
            }
            log.info("已从文件加载白名单缓存，共 {} 个充电桩", whiteCardCache.size());
        } catch (IOException e) {
            log.error("从文件加载白名单缓存失败: {}", e.getMessage(), e);
            // 加载失败时清空缓存，防止部分加载导致的数据不一致
            whiteCardCache.clear();
        } catch (OutOfMemoryError e) {
            log.error("加载白名单缓存内存不足: {}", e.getMessage());
            whiteCardCache.clear();
            // 强制GC尝试释放内存
            System.gc();
        }
    }

}