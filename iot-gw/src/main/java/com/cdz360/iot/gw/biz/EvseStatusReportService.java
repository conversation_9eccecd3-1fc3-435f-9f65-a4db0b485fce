package com.cdz360.iot.gw.biz;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.biz.ds.EvseSignKeyCache;
import com.cdz360.iot.gw.biz.ds.EvseSocketCache;
import com.cdz360.iot.gw.biz.ds.PlugCache;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.GwPlugVo;
import com.cdz360.iot.gw.model.base.DecimalRange;
import com.cdz360.iot.gw.model.base.SocketInfo;
import com.cdz360.iot.gw.model.connection.EvseConnection;
import com.cdz360.iot.gw.model.connection.PlugConnection;
import com.cdz360.iot.gw.model.converter.ErrorCodeConverter;
import com.cdz360.iot.gw.model.evse.ErrorCodeMsg;
import com.cdz360.iot.gw.model.evse.protocol.EvseRegisterUp;
import com.cdz360.iot.gw.model.gw.CloudUpReq;
import com.cdz360.iot.gw.model.type.ConstantCollections;
import com.cdz360.iot.gw.north.model.CloudEvsePasscodeReq;
import com.cdz360.iot.gw.north.model.CloudEvsePasscodeRes;
import com.cdz360.iot.gw.north.model.CloudEvseRegisterReq;
import com.cdz360.iot.gw.north.model.EvsePlug;
import com.cdz360.iot.gw.north.model.EvseStatusRequest;
import com.cdz360.iot.gw.north.server.EvseService;
import com.cdz360.iot.gw.south.server.SocketRepository;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import eu.chargetime.ocpp.AuthenticationException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EvseStatusReportService {

    private final Logger logger = LoggerFactory.getLogger(EvseStatusReportService.class);

    // @Autowired
    // private IotUpService iotUpService;

    @Autowired
    private EvseRepository evseRepository;

    @Autowired
    private EvseSignKeyCache evseSignKeyCache;

    @Autowired
    private EvseSocketCache evseSocketCache;

    @Autowired
    private PlugCache plugCache;

    @Autowired
    private EvseService evseService;

    @Autowired
    private SocketRepository socketRepository;

    public boolean authenticateSessionProcess(String chKey, String evseNo, String password)
        throws AuthenticationException {
        if (StringUtils.isBlank(evseNo)) {
            log.error("[{}] 桩编号不能为空. evseNo: {}", chKey, evseNo);
            throw new AuthenticationException(401, "Invalid ResourceDescriptor");
        }

        String expectedPasscode = evseSignKeyCache.get(evseNo);
        if (expectedPasscode == null) {
            // 缓存中不存在密钥时从云端获取
            expectedPasscode = this.fetchEvsePasscode(evseNo);
        } else {
            boolean equals = expectedPasscode.equals(password);
            if (!equals) {
                // 若与缓存中的密钥不匹配，则从云端获取最新值
                expectedPasscode = this.fetchEvsePasscode(evseNo);
            }
        }
        if (StringUtils.isBlank(expectedPasscode)) {
            log.info("[{} {}] 长效密钥为空时不校验.", chKey, evseNo);
            return true;
        }
        logger.info("[{} {}] 长效密钥校验. expectedPasscode: {}, reported password: {}", chKey,
            evseNo, expectedPasscode, password);
        return expectedPasscode.equals(password);
    }

    private String fetchEvsePasscode(String evseNo) {
        String seq = SeqGeneratorUtil.newStringId();
        CloudUpReq<CloudEvsePasscodeReq> cloudReq = new CloudUpReq<>();
        cloudReq.setData(new CloudEvsePasscodeReq()).setSeq(seq);
        cloudReq.getData().setEvseNo(evseNo);
        CloudEvsePasscodeRes passcodeRes = this.evseService.getEvsePasscode(seq, cloudReq);
        if (passcodeRes != null && !StringUtils.isEmpty(passcodeRes.getPasscode())) {
            evseSignKeyCache.put(evseNo, passcodeRes.getPasscode());
        }
        return passcodeRes.getPasscode();
    }

    public void registerEvse(String traceId, GwEvseVo evse, EvseRegisterUp evseReq) {
        CloudUpReq<CloudEvseRegisterReq> reqMsg = this.buildReqMsg(evse, evseReq);
        evseService.registerEvse(traceId, reqMsg);
        //return mono;
    }

    private CloudUpReq<CloudEvseRegisterReq> buildReqMsg(GwEvseVo evse, EvseRegisterUp evseReq) {
        CloudEvseRegisterReq reqMsg = new CloudEvseRegisterReq();
        CloudUpReq<CloudEvseRegisterReq> msg = new CloudUpReq<>();
        msg.setSeq(SeqGeneratorUtil.newStringId()).setData(reqMsg);
        reqMsg.setEvseNo(evse.getEvseNo()).setProtocolVer(evse.getProtocolVer())
            .setPower(BigDecimal.valueOf(evseReq.getPower())).setPriceCode(evseReq.getPriceCode())
            .setSupplyType(evse.getSupplyType().name()).setNetType(evse.getNet().getEthName())
            .setDtuType(evseReq.getDtuType()).setEvseIp(evseReq.getBase().getEvseIp())
            .setIccid(evseReq.getIccid()).setImei(evseReq.getImei()).setImsi(evseReq.getImsi())
            .setRegisterReason(evseReq.getRegisterReason()).setPlugNum(evse.getPlugNum())
            .setPasscodeVer(evseReq.getPasscodeVer());

        List<CloudEvseRegisterReq.PcInfo> pcVer = new ArrayList<>();
        if (evse.getPc01Ver() != null) {
            pcVer.add(new CloudEvseRegisterReq.PcInfo("PC01", null, evse.getPc01Ver(), null));
        }
        if (evse.getPc02Ver() != null) {
            pcVer.add(new CloudEvseRegisterReq.PcInfo("PC02", null, evse.getPc02Ver(), null));
        }
        if (evse.getPc03Ver() != null) {
            pcVer.add(new CloudEvseRegisterReq.PcInfo("PC03", null, evse.getPc03Ver(), null));
        }
        reqMsg.setPcVer(pcVer);
        reqMsg.setFirmwareVer(evse.getFirmwareVer());

        if (evse.getSupplyType() == SupplyType.AC) {
            this.buildAcPlugs(evseReq, reqMsg);
        } else if (evse.getSupplyType() == SupplyType.DC) {
            this.buildDcPlugs(evseReq, reqMsg);
        } else {
            log.error("不支持的电流类型");
        }
        return msg;
    }

    private void buildAcPlugs(EvseRegisterUp evseReq, CloudEvseRegisterReq reqMsg) {
        List<CloudEvseRegisterReq.PlugInfo> plugs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(evseReq.getAcPlugs())) {
            evseReq.getAcPlugs().stream().forEach(p -> {

                CloudEvseRegisterReq.PlugInfo plug = new CloudEvseRegisterReq.PlugInfo();
                plug.setPlugId(p.getPlugIdx());

                if (p.getPower() != null) {
                    plug.setPower(BigDecimal.valueOf(p.getPower()));
                }

                if (p.getVoltage() != null) {
                    plug.setVoltage(new DecimalRange(DecimalUtils.divide10(p.getVoltage()),
                        DecimalUtils.divide10(p.getVoltage())));
                }

                // plug.setCurrent(new DecimalRange(null, null)); // 交流桩没有额定电流

                plugs.add(plug);
            });
            reqMsg.setPlugs(plugs);
        }
    }

    private void buildDcPlugs(EvseRegisterUp evseReq, CloudEvseRegisterReq reqMsg) {
        List<CloudEvseRegisterReq.PlugInfo> plugs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(evseReq.getDcPlugs())) {
            evseReq.getDcPlugs().forEach(p -> {

                CloudEvseRegisterReq.PlugInfo plug = new CloudEvseRegisterReq.PlugInfo();
                plug.setPlugId(p.getPlugIdx());

                if (p.getPower() != null) {
                    plug.setPower(BigDecimal.valueOf(p.getPower()));
                }

                if (evseReq.getMinVoltage() != null && evseReq.getMaxVoltage() != null) {
                    plug.setVoltage(new DecimalRange(DecimalUtils.divide10(evseReq.getMinVoltage()),
                        DecimalUtils.divide10(evseReq.getMaxVoltage())));
                }

                if (p.getMinCurrent() != null && p.getMaxCurrent() != null) {
                    plug.setCurrent(new DecimalRange(DecimalUtils.divide10(p.getMinCurrent()),
                        DecimalUtils.divide10(p.getMaxCurrent())));
                }

                plugs.add(plug);
            });
        }
        reqMsg.setPlugs(plugs);
    }

    // FIXME: 这里需要调整逻辑
    private EvseStatusRequest tranEvseStatusRequest(String traceId, GwEvseVo evse, Integer plugId) {

        //logger.info("转换桩端的桩实体到云端。traceId: {}, evse: {}", traceId, evse);

//        byte elec = SupplyType.valueOf(evse.getSupplyType().name()) == SupplyType.AC ?
//                ConstantCollections.AC : ConstantCollections.DC;
        ErrorCodeMsg codeMsg = ErrorCodeConverter.convert(evse.getSupplyType(),
            evse.getErrorCode());

        EvseStatusRequest evseStatusRequest = new EvseStatusRequest() {
            {
                setEvseNo(evse.getEvseNo());                //桩在云平台的唯一ID
                setEvseStatus(evse.getStatus().name());
                setTemp(evse.getTemperature());                //桩温度
                setErrorCode(codeMsg.getCode());           //枪异常问题编码
                setError(codeMsg.getMsg());                //桩异常问题描述
            }
        };

        List<EvsePlug> plugs = new ArrayList<>();

        List<GwPlugVo> guns = evse.getPlugs();
        if (!CollectionUtils.isEmpty(guns)) {
            for (GwPlugVo gun : guns) {
                if (null == plugId || Objects.equals(gun.getIdx(), plugId)) {
                    EvsePlug plug = new EvsePlug() {{
                        setPlugId(gun.getIdx());             //是充电枪ID
                        setPlugStatus(
                            gun.getStatus().name());    //充电枪状态. 空闲, 等待开始充电, 充电中, 充电完成, 不可用 ???
//                    setErrorCode(gunCodeMsg.getCode());    //桩端状态码
//                    setError(gunCodeMsg.getMsg());         //当枪状态异常时, 上传异常原因
                        setAlertCode(gun.getAlertCode());
                        setTemp(gun.getTemperature());             //充电枪温度
                        setOrderNo(gun.getOrderNo()); // 充电订单号
                    }};

                    // 枪异常信息
                    if (null != gun.getErrorCode()) {
                        plug.setErrorCode(codeMsg.getCode());
                        plug.setError(codeMsg.getMsg());
                    }

                    plugs.add(plug);
                }
            }
        }

        evseStatusRequest.setPlugs(plugs);

        //logger.info("转换桩端的桩实体到云端的结果。traceId: {}, evseStatusRequest: {}", traceId, evseStatusRequest);

        return evseStatusRequest;
    }

    /**
     * 桩状态更新，向云端发送 1、在桩注册时 2、在桩变化时，处理桩端心跳processHeartbeat
     *
     * @param traceId
     * @param evse
     * @param plugId  指定枪头编号进上传该枪状态，其他不上传
     */
    public void reportStatus(String traceId, GwEvseVo evse, Integer plugId) {
        try {

            EvseStatusRequest evseStatusRequest = tranEvseStatusRequest(traceId, evse, plugId);

            // 桩状态上传云
            evseService.reportEvseStatus(traceId, evseStatusRequest);

        } catch (Exception ex) {
            logger.error("上报桩状态时发生错误。traceId: {}", traceId, ex);
        }

        try {
            if (CollectionUtils.isEmpty(evse.getPlugs())) {
                logger.warn("桩下没有任何枪。traceId: {}, evse: {}", traceId, evse);
                return;
            }

            evse.getPlugs().forEach(x -> {
                PlugConnection connection = plugCache.get(x.getEvseNo(), x.getIdx());
                if (connection != null) {
                    connection.setLastReportTime(new Date());
                }
            });

        } catch (Exception ex) {
            logger.error("修改枪缓存时发生错误。traceId: {}, ", traceId, ex);
        }
    }

    public void reportWhenException(String channelKey) {
        logger.info("异常时上报桩状态。channelKey: {}", channelKey);

        GwEvseVo evse = getEvse(channelKey);

        report("EXCEPTION", channelKey, evse);
    }

    public void reportWhenTimeout(String channelKey) {
        logger.info("桩离线时上报桩状态。channelKey: {}", channelKey);

        GwEvseVo evse = getEvse(channelKey);

        report("EVSE_TIMEOUT_" + SeqGeneratorUtil.newStringId(), channelKey, evse);

        syncCache(channelKey, evse);
    }

    private void report(String tid, String channelKey, GwEvseVo evse) {
        logger.info("[{}] 上报桩状态。 channelKey: {}, evse: {}", tid, channelKey, evse);

        if (evse == null) {
            logger.info("[{}] 上报桩状态时本地无此桩的缓存信息。 channelKey: {}", tid, channelKey);
            return;
        }

        // 判断桩是否正真断线
        if (StringUtils.isEmpty(evse.getEvseNo())) {
            logger.info("[{} {}] 本地缓存的桩没有桩编号,退出桩状态上报。 channelKey: {}", tid,
                evse.getEvseNo(), channelKey);
            return;
        }

        EvseConnection evseConnection = evseSocketCache.get(evse.getEvseNo());
        if (null != evseConnection && !evseConnection.getClientAddress()
            .equals(channelKey)) { // 连个连接的地址不一样，认为重连了
            logger.info("[{} {}] 桩端已经重连，无需上报离线状态: channelKey: {}", tid,
                evse.getEvseNo(), channelKey);
            return;
        }

        try {
            logger.info("[{} {}] 上报桩状态-设置本地缓存为离线。", tid, evse.getEvseNo());
            evseRepository.offlineEvse(tid,
                evse);//TODO:目前是只要有一个枪头没有数据就把整个桩和桩下面的枪设置为离线。但是由于一个桩有多个枪，这个是不合理的。后面考虑方案解决 190722 朱子成
        } catch (Exception e) {
            logger.error("[{} {}] 上报桩状态-设置本地缓存为离线失败。channelKey: {}, evse: {}", tid,
                evse.getEvseNo(), channelKey, evse, e);
        }

        try {
            logger.info("[{} {}] 上报桩状态-上报云端。", tid, evse.getEvseNo());
            reportStatus(tid, evse, null);
        } catch (Exception e) {
            logger.error("[{}] 上报桩状态-上报云端失败。channelKey: {}, evse: {}", tid, channelKey,
                evse, e);
        }
    }

    private void syncCache(String channelKey, GwEvseVo evse) {

        logger.info("同步本地桩数据缓存。evse: {}", evse);

        if (evse == null) {
            logger.info("同步本地桩数据缓存时本地无此桩的缓存信息。channelKey: {}", channelKey);
            return;
        }

        //删除枪缓存
        try {

            int count = evse.getPlugNum();

            for (int i = ConstantCollections.PLUG_FIRST_NO; i <= count; i++) {
                logger.debug("删除枪缓存。evseNo: {}, plugNo: {}, plug heartbeat: {}",
                    evse.getEvseNo(), i, plugCache.get(evse.getEvseNo(), i));

                // plugCache.remove(evse.getNo(), i);//TODO: 由于目前无法区分枪号，如果有检测到桩/枪没有心跳，是直接将整个桩作为单位上报离线。因此下面的逻辑暂时搁置。 朱子成 2019-7-31
                // TODO: 枪头状态补传机制依赖于PlugCache, remove之前需要确保此机制对PlugCache无依赖 2019-08-19 朱子成
            }

        } catch (Exception ex) {
            logger.error("同步本地枪缓存失败。channelKey: {}, evse: {}", channelKey, evse, ex);
        }

        //删除桩缓存
        try {
            EvseConnection connection = evseSocketCache.get(evse.getEvseNo());

            if (connection != null) {

                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

                long millis = Duration.between(connection.getHeartbeatTime().toInstant(),
                    Instant.now()).toMillis();//计算两个时间之间的间隔毫秒

                logger.info(
                    "桩连接已经过期。evseNo: {}, channelKey: {}, 上次心跳时间为：{}, 间隔: {}毫秒，注册时间：{}",
                    connection.getEvseNo(), channelKey,
                    format.format(connection.getHeartbeatTime()), millis,
                    format.format(connection.getRegisterTime()));

                evseSocketCache.removeByValue(connection);
            }

        } catch (Exception ex) {
            logger.error("同步本地桩数据缓存失败。channelKey: {}, evse: {}", channelKey, evse, ex);
        }
    }

    private GwEvseVo getEvse(String channelKey) {
        try {
            SocketInfo socketInfo = socketRepository.get(channelKey);
            String evseNo = socketInfo.getEvseNo();
            EvseConnection conn = this.evseSocketCache.get(evseNo);  // 桩编号对应的tcp连接
            if (conn != null && StringUtils.equals(conn.getClientAddress(), channelKey)) {
                GwEvseVo evse = null;

                if (!StringUtils.isEmpty(evseNo)) {
                    evse = evseRepository.getEvse(evseNo);
                }
                logger.info("桩信息：channelKey: {}, evse: {}", channelKey, evse);
                return evse;
            } else {
                logger.warn("桩 {} 当前对应的TCP连接为 {}", evseNo, conn);
            }
        } catch (Exception e) {
            logger.error("获取离线桩失败。channelKey: {}", channelKey, e);
        }
        return null;
    }

}
