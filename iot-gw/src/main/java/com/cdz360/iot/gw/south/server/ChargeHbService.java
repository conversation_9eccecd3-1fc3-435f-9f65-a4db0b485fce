package com.cdz360.iot.gw.south.server;

import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.vo.ChargePriceItem;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.CacheManagerService;
import com.cdz360.iot.gw.biz.EvseStatusReportService;
import com.cdz360.iot.gw.biz.ds.EvseOrderRepository;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.biz.ds.IdempotenceService;
import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.core.biz.OrderBizService;
import com.cdz360.iot.gw.model.EvseContext;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.GwPlugVo;
import com.cdz360.iot.gw.model.evse.protocol.ChargeHbUp;
import com.cdz360.iot.gw.model.evse.protocol.ChargeStopDown;
import com.cdz360.iot.gw.north.model.OrderData;
import com.cdz360.iot.gw.north.model.OrderDetail;
import com.cdz360.iot.gw.north.model.OrderTimeIntervalDetail;
import com.cdz360.iot.gw.north.model.OrderUpdateData;
import com.cdz360.iot.gw.north.server.NorthOrderService;
import com.cdz360.iot.gw.util.PriceUtil;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class ChargeHbService {

    private final static Integer DETAIL_MAX_SIZE = 20;

    private final static String GW_EVSE_HB = "HB:EVSE";
    private static final List<PlugStatus> PLUG_BUSY_STATUS_LIST = List.of(PlugStatus.BUSY,
        PlugStatus.JOIN, PlugStatus.RECHARGE_END);

    @Autowired
    private IdempotenceService idempotenceService;

    @Autowired
    private EvseRepository evseRepository;

    @Autowired
    private EvseOrderRepository evseOrderRepository;

    @Autowired
    private EvseStatusReportService evseStatusReportService;

    @Autowired
    private NorthOrderService orderService;

    @Autowired
    private CacheManagerService cacheManagerService;

    @Autowired
    private OrderBizService orderBizService;

    @Value("${cdz360.timer.heartbeat-timeout:120000}")
    private int heartbeatTimeout;

    @Value("${cdz360.merge-num:4}")
    private int mergeNum;//合并发送数

    /**
     * 根据传入的时间获取对应的价格信息
     *
     * @param mvHbTime 需要判断的时间
     * @param priceVo  价格信息对象
     * @return 对应时间段的价格信息，如果没有匹配到则返回null
     */
    private ChargePriceItem getPriceByTime(ZonedDateTime mvHbTime, ChargePriceVo priceVo) {

        if (mvHbTime == null || priceVo == null || CollectionUtils.isEmpty(priceVo.getItemList())) {
            return null;
        }

        // 转换到系统默认时区
//        ZonedDateTime localDateTime = mvHbTime.withZoneSameInstant(ZoneId.systemDefault());

        // 获取当前小时和分钟
        LocalTime currentTime = mvHbTime.toLocalTime();

        // 遍历价格列表，查找匹配的时间段
        for (ChargePriceItem price : priceVo.getItemList()) {
            LocalTime startTime = LocalTime.parse(PriceUtil.formatTimeString(price.getStartTime()),
                DateTimeFormatter.ofPattern("HH:mm"));
            LocalTime stopTime = LocalTime.parse(PriceUtil.formatTimeString(price.getEndTime()),
                DateTimeFormatter.ofPattern("HH:mm"));

            // 判断当前时间是否在时间段内
            if (isTimeInRange(currentTime, startTime, stopTime)) {
                return price;
            }
        }
        return null;
    }

    /**
     * 判断当前时间是否在指定的时间范围内
     *
     * @param currentTime 当前时间
     * @param startTime   开始时间
     * @param stopTime    结束时间
     * @return 是否在范围内
     */
    private boolean isTimeInRange(LocalTime currentTime, LocalTime startTime,
        LocalTime stopTime) {
        // 处理跨天的情况（如23:00-01:00）
        if (stopTime.equals(LocalTime.MIDNIGHT)) {
            // 如果结束时间是00:00，视为24:00
            return !currentTime.isBefore(startTime);
        } else if (stopTime.isBefore(startTime)) {
            return !currentTime.isBefore(startTime) || !currentTime.isAfter(stopTime);
        }

        // 普通情况
        return !currentTime.isBefore(startTime) && currentTime.isBefore(stopTime);
    }

    public void processHeartBeat(ChargeHbUp msg) {
        log.debug("[{} {}] 处理订单心跳开始. plugId = {}",
            msg.getBase().getTid(), msg.getBase().getEvseNo(), msg.getBase().getPlugIdx());
        String evseNo = msg.getBase().getEvseNo();

        GwEvseVo evseCache = evseRepository.getEvse(evseNo); //先获取注册时本地缓存的桩

        int plugIdx = msg.getBase().getPlugIdx();

        Optional<GwPlugVo> plugOpt = cacheManagerService.getPlugCache(evseNo, plugIdx);
        if (plugOpt.isEmpty()) {
            log.error("[{} {}] 获取枪头缓存失败. plugIdx = {}",
                msg.getBase().getTid(), evseNo, plugIdx);
            return;
        }
        GwPlugVo plug = plugOpt.get();
        plug.setStatus(PlugStatus.BUSY);//解决3.7协议充电中DTU闪断，导致桩状态一直处于离线

        EvseContext ctx = new EvseContext(msg.getBase().getTid(), evseNo, plugIdx, msg, evseCache);

        // 添加到桩编号 Channel Key 映射管理

        if (evseCache == null //|| plugHeartbeat == null
        ) {
            log.info("[{} {}] 桩/枪没有注册。", msg.getBase().getTid(), evseNo);
            return;
        }

//        ChargeHbReq evseReq;
//        evseReq = ChargeHbDecoder.parseX(msg.getTraceId(), evseCache, msg.getFullMsg());
//
//        evseReq.setBase(msg.getBaseMsg());

        log.trace("[{} {}] chargeHbReq = {}", msg.getBase().getTid(), evseNo, msg);
        this.processOrder(ctx, msg, plug);

        if (StringUtils.isEmpty(idempotenceService.get(GW_EVSE_HB + evseNo))) {
            log.debug("[{} {}] 设置桩全局心跳缓存。", msg.getBase().getTid(),
                msg.getBase().getEvseNo());
            idempotenceService.put(GW_EVSE_HB + evseNo, evseNo);
        }

        boolean isChanged = evseRepository.updateGun(ctx, plug);// 更新状态,并且返回更新标识

        // 更新 evseCache 其他属性值
        if (isChanged) {
            this.reportEvseStatus(ctx.getTraceId(), ctx.getEvse(), ctx.getPlugIdx());
            idempotenceService.remove(GW_EVSE_HB + evseNo);
            return;
        } else {
            log.debug("[{} {}] 未更新桩枪状态", msg.getBase().getTid(), msg.getBase().getEvseNo());
        }


    }


    private void processOrder(EvseContext ctx, ChargeHbUp evseReq, GwPlugVo plug) {
        // TODO: 2025/4/21 WZFIX 待删除日志
        log.info("[{} {}] 处理订单心跳. evseReq = {}", ctx.getTraceId(), ctx.getEvseNo(), evseReq);
        try {
            String tid = ctx.getTraceId();
            // 获取缓存中的订单
            OrderData order = evseOrderRepository.getOrder(evseReq.getOrderNo());
            // TODO: 2025/3/10 WZFIX 待删除日志
            log.info("[{} {}] 获取缓存中的订单信息: orderNo = {}, OrderData: {}", tid,
                ctx.getEvseNo(), evseReq.getOrderNo(), JsonUtils.toJsonString(order));
            if (null == order) {
                log.error("[{} {}] 缓存中都没有该订单信息: orderNo = {}", tid, ctx.getEvseNo(),
                    evseReq.getOrderNo());
                return;
            }

            if (order.getBillingDetails() == null) {
                order.setBillingDetails(new HashMap<>());
            }
            if (order.getDetails() == null) {
                order.setDetails(new ArrayList<>());
            }

            Pair<BigDecimal, BigDecimal> pair = this.calcOrderFee(tid,
                ctx.getEvseNo(), evseReq.getMvHbTime(), order);
            // TODO: 2025/3/10 WZFIX 待删除日志
            log.info("[{}] 计算订单费用: orderNo = {}, elecFee = {}, servFee = {}",
                tid, order.getOrderNo(), pair.getLeft(), pair.getRight());
            if (evseReq.getElecFee() == null) { // RCD桩充电中会上传电费
                evseReq.setElecFee(pair.getLeft());
            }
            if (evseReq.getServFee() == null) { // RCD桩充电中会上传服务费
                evseReq.setServFee(pair.getRight());
            }
            OrderTimeIntervalDetail detail = getOrderDetail(ctx, evseReq);
            //if(detail != null) {
            order.getDetails().add(detail);
            //}

            log.info(
                "[{} {}] plugIdx = {}, orderNo = {}, orderStatus = {}, isUploadHb = {}, details.size = {}",
                ctx.getTraceId(), order.getEvseNo(), order.getIdx(), order.getOrderNo(),
                order.getOrderStatus(), order.isUploadHb(), order.getDetails().size());
            //开启订单后第一次心跳直接上报，其他的满10次上报
            if (order.isUploadHb()) {
//                this.powerCtrl(ctx, evseReq);   // 功率动态分配
                order.setUploadHb(false);
                this.send2Cloud(ctx.getTraceId(), ctx.getEvseNo(), ctx.getPlugIdx(), order);
            } else if (order.getDetails().size() >= mergeNum) {
//                this.powerCtrl(ctx, evseReq);   // 功率动态分配
                this.send2Cloud(ctx.getTraceId(), ctx.getEvseNo(), ctx.getPlugIdx(), order);
            }

            //更新到缓存
            evseOrderRepository.hbUpdateOrder(ctx.getTraceId(), order, evseReq.getBase().getSeq());

            // 充电完成状态不需要下发
            if (plug.getStatus() == PlugStatus.BUSY) {
                this.checkOverCharge(ctx.getTraceId(), order, detail, ctx.getEvseNo(),
                    ctx.getPlugIdx());
            }
        } catch (Exception ex) {
            log.error("[{} {}] 心跳中处理订单数据失败。", ctx.getTraceId(), ctx.getEvseNo(), ex);
        }
    }


    private void checkOverCharge(String traceId, OrderData order, OrderTimeIntervalDetail detail,
        String evseNo, int plugIdx) {
        if (IotGwConstants.PAY_ACCOUNT_TYPE_PREPAY.equalsIgnoreCase(order.getAccountType())) {
            // 即充即退的订单要防止桩端超充
            BigDecimal fee = detail.getElecFee().add(detail.getServFee());
            if (DecimalUtils.gt(fee, order.getAmount())) {
                // 充超要下发停止指令
                log.warn(
                    "[{} {}] 桩端充电金额超过订单冻结金额!!! orderNo = {}, frozenAmount = {}, order.fee = {}",
                    traceId, evseNo, order.getOrderNo(), order.getAmount(), fee);

                EvseMsgBase base = new EvseMsgBase();
                base.setEvseNo(evseNo)
                    .setPlugIdx(plugIdx)
                    .setCmdCode(EvseMessageType.CHARGE_FROM_CLOUD)
//                    .setSeq(mqMsg.getSeqNoToEvse())
                ;

                ChargeStopDown dMsg = new ChargeStopDown(base);
                dMsg.setOrderNo(order.getOrderNo())
                ;
                this.orderBizService.stopOrderFromCloud(dMsg);
            }
        }
    }

    /*
    // 功率动态分配
    private void powerCtrl(EvseContext ctx, ChargeHbUp evseReq) {
        Integer power = this.sitePowerBizService.hb(ctx.getTraceId(), ctx.getEvseNo(),
            ctx.getPlugIdx(), evseReq.getRequestPower().intValue());
        if (power != null && power.intValue() > 0) {
            //
            ChargePowerCtrlDown dMsg = new ChargePowerCtrlDown(evseReq.getBase());
            dMsg.setOrderNo(evseReq.getOrderNo());
            dMsg.setPowerCtrl(power);
            chargePowerCtrlCommander.sendReply(ctx.getTraceId(), dMsg);
        }
    }
    */

    /**
     * 计算订单费用
     * @param tid
     * @param evseNo
     * @param mvHbTime
     * @param order
     * @return
     */
    private Pair<BigDecimal, BigDecimal> calcOrderFee(String tid, String evseNo,
        ZonedDateTime mvHbTime, OrderData order) {
        log.info("[{} {}] mvHbTime: {}, order: {}", tid, evseNo, mvHbTime,
            JsonUtils.toJsonString(order));

        ChargePriceItem priceItem = this.getPriceByTime(mvHbTime, order.getPrice());
        log.debug("[{} {}] 获取价格信息 priceItem: {}", tid, evseNo,
            JsonUtils.toJsonString(priceItem));

        OrderDetail lastOrderDetail = order.getLastOrderDetail();

        if (priceItem != null) {
            if (lastOrderDetail == null) {
                log.info("[{} {}] 时段计费数据缺失，仅记录充放电量. orderNo = {}", tid, evseNo,
                    order.getOrderNo());
                BigDecimal startMeter =
                    order.getBillingDetails() == null || order.getBillingDetails().isEmpty()
                        ? order.getStartMeter() : order.getCurrMeter();

                OrderDetail newOrderDetail = new OrderDetail();
                newOrderDetail.setStartTimeStr(priceItem.getStartTime())
                    .setStopTimeStr(priceItem.getEndTime())
                    .setStartTime(mvHbTime.toEpochSecond())
                    .setPriceCode(priceItem.getCode())
                    .setElecPrice(priceItem.getElecPrice())
                    .setServPrice(priceItem.getServPrice())
                    .setStartMeter(startMeter)
                    .setKwh(BigDecimal.ZERO);
                order.setLastOrderDetail(newOrderDetail);
                evseOrderRepository.updateOrder(order);
                return Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
            }

            if (!lastOrderDetail.getStartTimeStr().equalsIgnoreCase(priceItem.getStartTime())
                && !lastOrderDetail.getStopTimeStr().equalsIgnoreCase(priceItem.getEndTime())) {
                log.info("[{} {}] 切换到新时段. orderNo: {}", tid, evseNo, order.getOrderNo());
                BigDecimal currKwh = order.getCurrMeter().subtract(lastOrderDetail.getStartMeter());
                BigDecimal elecFee = lastOrderDetail.getElecPrice().multiply(currKwh)
                    .setScale(4, RoundingMode.HALF_UP);
                BigDecimal servFee = lastOrderDetail.getServPrice().multiply(currKwh)
                    .setScale(4, RoundingMode.HALF_UP);
                lastOrderDetail.setKwh(currKwh)
                    .setElecFee(elecFee)
                    .setServFee(servFee);

                Pair<Integer, Integer> pair = PriceUtil.splitTimeStr(priceItem.getStartTime());
                ZonedDateTime currStopTime = mvHbTime.with(LocalTime.MIN).withHour(pair.getLeft())
                    .withMinute(pair.getRight());
                // 记录上一时段的结束时间
                lastOrderDetail.setStopTime(currStopTime.toEpochSecond());
                order.pushBillingDetail(lastOrderDetail);

                OrderDetail newOrderDetail = new OrderDetail();
                newOrderDetail.setStartTimeStr(priceItem.getStartTime())
                    .setStopTimeStr(priceItem.getEndTime())
                    .setStartTime(lastOrderDetail.getStopTime())
                    .setPriceCode(priceItem.getCode())
                    .setElecPrice(priceItem.getElecPrice())
                    .setServPrice(priceItem.getServPrice())
                    .setStartMeter(order.getCurrMeter())
                    .setKwh(BigDecimal.ZERO);
                order.setLastOrderDetail(newOrderDetail);

            } else {
                if (order.getCurrMeter() != null && lastOrderDetail.getStartMeter() != null) {
                    BigDecimal currKwh = order.getCurrMeter()
                        .subtract(lastOrderDetail.getStartMeter());

                    lastOrderDetail.setKwh(currKwh)
                        .setElecFee(priceItem.getElecPrice().multiply(currKwh)
                            .setScale(4, RoundingMode.HALF_UP))
                        .setServFee(priceItem.getServPrice().multiply(currKwh)
                            .setScale(4, RoundingMode.HALF_UP));
                    order.setLastOrderDetail(lastOrderDetail);
                }
            }
            evseOrderRepository.updateOrder(order);

        } else {
            log.error("[{} {}] 获取价格信息失败 orderNo: {}", tid, evseNo, order.getOrderNo());
        }

        if (lastOrderDetail == null) {
            return Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
        }

        AtomicReference<BigDecimal> elecFeeAto = new AtomicReference<>(
            lastOrderDetail.getElecFee());
        AtomicReference<BigDecimal> servFeeAto = new AtomicReference<>(
            lastOrderDetail.getServFee());

        order.getBillingDetails().forEach((k, v) -> {
            elecFeeAto.set(DecimalUtils.add(elecFeeAto.get(), v.getElecFee()));
            servFeeAto.set(DecimalUtils.add(servFeeAto.get(), v.getServFee()));
        });
        return Pair.of(elecFeeAto.get(), servFeeAto.get());
    }

    private OrderTimeIntervalDetail getOrderDetail(
        EvseContext ctx, ChargeHbUp evseReq) {

        GwEvseVo evse = ctx.getEvse();

        OrderTimeIntervalDetail detail = new OrderTimeIntervalDetail();
        //long timestamp = getTime(evseReq, order);
        detail.setTimestamp(evseReq.getBase().getTimestamp());//时间戳
        detail.setDuration(evseReq.getDuration());
        detail.setRemainingTime(evseReq.getRemainTime());
        detail.setKwh(evseReq.getKwh());
        detail.setElecFee(evseReq.getElecFee());
        detail.setServFee(evseReq.getServFee());
        //detail.setBalance();
        if (evseReq.getBattery() != null) {
            detail.setBatteryTemp(evseReq.getBattery().getTemp());
        }
        detail.setEvseTemp(evseReq.getEvseTemp());
        detail.setPlugTemp(evseReq.getPlugTemp());
        //detail.setMaxVoltage(DecimalUtils.divide100(evseReq.getMaxVoltage()));
        //detail.setMinVoltage(DecimalUtils.divide100(evseReq.getMinVoltage()));
        detail.setBms(evseReq.getBms());
        detail.setBattery(evseReq.getBattery());

        if (evseReq.getSoc() != null) {
            detail.setSoc(evseReq.getSoc() == 0xFF ? null : evseReq.getSoc());
        }
        detail.setPower(evseReq.getPower());

        //交流
        if (evse.getSupplyType() == SupplyType.AC) {
            //电压
            detail.setAcVoltageA(evseReq.getVoltageA());
            detail.setAcVoltageB(evseReq.getVoltageB());
            detail.setAcVoltageC(evseReq.getVoltageC());

            //电流
            detail.setAcCurrentA(evseReq.getCurrentA());
            detail.setAcCurrentB(evseReq.getCurrentB());
            detail.setAcCurrentC(evseReq.getCurrentC());
        }

        //直流
        if (evse.getSupplyType() == SupplyType.DC) {
            detail.setDcVoltageO(evseReq.getOutputVoltage());
            detail.setDcCurrentO(evseReq.getOutputCurrent());
            detail.setDcVoltageA(evseReq.getVoltageA());
            detail.setDcVoltageB(evseReq.getVoltageB());
            detail.setDcVoltageC(evseReq.getVoltageC());
        }

        log.debug("[{} {}] 订单分时信息。 detail: {}", ctx.getTraceId(), ctx.getEvseNo(), detail);

        return detail;
    }

    private void send2Cloud(String traceId, String evseNo, int plugNo, OrderData order) {

        //上报update 分时段详情
        OrderUpdateData updateReq = orderService.transOrderUpdateRequest(evseNo, plugNo, order);
        orderService.updateOrder(traceId, updateReq)
            .subscribe(a -> order.getDetails().clear());//上报后清掉分时段详情


    }

    // 指定枪头编号进上传该枪状态，其他不上传
    private void reportEvseStatus(String traceId, GwEvseVo evse, Integer plugIdx) {

        if (evse != null) {
//            // 调用方都转换了，这里可以注释
            evseStatusReportService.reportStatus(traceId, evse, plugIdx);
        } else {
            log.warn("[{}] evse is null!!! ", traceId);
        }
    }


    private boolean isValid(String orderNo) {
        boolean exist = false;
        if (orderNo != null && !StringUtils.isEmpty(orderNo)) {
            exist = !StringUtils.isEmpty(orderNo.replace("0", ""));
        }
        return exist;
    }
}
