package com.cdz360.iot.gw.south.handler;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.CacheManagerServiceImpl;
import com.cdz360.iot.gw.biz.ds.EvseOrderRepository;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.core.biz.OrderBizService;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.converter.AuthCodeConverter;
import com.cdz360.iot.gw.model.evse.protocol.ChargeStartedUp;
import com.cdz360.iot.gw.model.evse.protocol.EvseTranx;
import com.cdz360.iot.gw.model.type.CalcType;
import com.cdz360.iot.gw.model.type.ConstantCollections;
import com.cdz360.iot.gw.north.model.OrderData;
import com.cdz360.iot.gw.north.model.OrderStartData;
import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.model.core.IdTagInfo;
import eu.chargetime.ocpp.model.core.MeterValuesConfirmation;
import eu.chargetime.ocpp.model.core.StartTransactionConfirmation;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * 上行 桩上报充电启动结果 6805
 */
@Slf4j
@Component
public class ChargeStartedHandler extends UpstreamHandler2 {


    @Autowired
    private OrderBizService orderService;

    @Autowired
    private EvseOrderRepository evseOrderRepository;

    @Autowired
    private CacheManagerServiceImpl cacheManagerService;

    @Autowired
    private OrderBizService orderBizService;

    @Autowired
    private EvseRepository evseRepository;

    @Override
    public EvseMessageType cmd() {
        return EvseMessageType.CHARGE_START;
    }

    @Override
    public Mono<EvseTranx<ChargeStartedUp, Confirmation>> processRequest(BaseEvseMsgUp msgIn) {
        String tid = msgIn.getBase().getTid();
        String evseNo = msgIn.getBase().getEvseNo();
        log.info("[{} {}] 桩端开始充电. ", tid, evseNo);

        ChargeStartedUp req = (ChargeStartedUp) msgIn;
        log.info("[{}] startTime: {}", tid, req.getStartTime());

        //缓存订单
        OrderData data = evseOrderRepository.getOrder(req.getTransId());
        if (data == null) {
            log.error("[{} {}] data为空. transId: {}", tid, evseNo, req.getTransId());
            throw new DcServiceException("订单逻辑异常");
        }

        data.setStartTime(req.getStartTime());
        data.setStopMode(req.getStopMode()); //停止方式
        OrderData orderData = evseOrderRepository.updateOrder(data);//add or update

        OrderStartData startRequest = makeRequest(req, orderData);
        log.info("[{} {}] req-startTime: {}, startRequest-startTime: {}", tid, evseNo,
            req.getStartTime(), startRequest.getStartTime());

        return orderService.orderStarted(msgIn.getBase().getTid(), startRequest)
            .doOnNext(non -> orderData.setUploadHb(true))   //  设置订单已经启动的标志位, 设置后心跳数据会立即上报
            .map(response -> {
                if (response == null || response.getStatus() != ConstantCollections.OK) {
                    log.error("[{} {}] 上传充电开始失败, 云端返回结果非预期。 response: {}",
                        tid, evseNo, response);
                    return new EvseTranx<>(req.getBase().getEvseNo(), req, null);
                } else {
                    OrderData updateOrder = new OrderData(data.getOrderNo());
                    updateOrder.setOrderStatus(ChargeOrderStatus.START);
                    evseOrderRepository.updateOrder(updateOrder);

                    this.fetchAndSaveOrderPriceInfo(tid, orderData.getOrderNo(), evseNo);

                    log.info("[{} {}] 上传充电开始到平台。 response: {}", tid, evseNo, response);
                }

                IdTagInfo idTagInfo = new IdTagInfo(
                    AuthCodeConverter.convert(response.getStatus()));
                Confirmation confirmation = this.assembleConfirmation(orderData, idTagInfo,
                    req.isStartTransReq());
                confirmation.setBase(req.getBase());

                return new EvseTranx<>(req.getBase().getEvseNo(), req, confirmation);
            });

    }

    /**
     * 将计费价格保存到订单上
     * @param tid
     * @param orderNo
     * @param evseNo
     */
    public void fetchAndSaveOrderPriceInfo(String tid, String orderNo, String evseNo) {
        log.info("[{} {}] 将计费价格保存到订单上. orderNo: {}", tid, evseNo, orderNo);
        OrderData data = new OrderData(orderNo);

        Optional<GwEvseVo> evseCache = cacheManagerService.getEvseCache(evseNo);
        if (evseCache.isPresent()) {
            log.info("[{} {}] 获取到桩缓存信息. evseCache: {}", tid, evseNo,
                JsonUtils.toJsonString(evseCache.get()));
            if (evseCache.get().getPriceCode() != null && evseCache.get().getPrice() != null) {
                GwEvseVo gwEvseVo = evseCache.get();
                data.setPriceCode(gwEvseVo.getPriceCode());
                data.setPrice(gwEvseVo.getPrice());

                evseOrderRepository.updateOrder(data);
                return;
            }
        }

        log.error("[{} {}] 未获取到桩本地计费信息, 从云端获取. orderNo: {}", tid, evseNo, orderNo);
        // TODO: 2025/4/21 WZFIX 应该直接获取订单上的计费信息，而不是桩缓存的计费
        orderBizService.fetchEvseCloudPrice(tid, evseNo)
            .doOnNext(priceVo -> {
                data.setPriceCode(priceVo.getId());

                data.setPrice(priceVo);
                evseOrderRepository.updateOrder(data);

                evseRepository.updateEvsePrice(evseNo, priceVo);
            })
            .subscribe();
    }

    public Confirmation assembleConfirmation(OrderData orderData, IdTagInfo idTagInfo,
        boolean startTransReqAto) {
        if (startTransReqAto) {
            return new StartTransactionConfirmation(idTagInfo, orderData.getTransId());
        }
        return new MeterValuesConfirmation();
    }

    private OrderStartData makeRequest(ChargeStartedUp req, OrderData orderData) {
        OrderStartData startRequest = new OrderStartData();
        startRequest.setEvseNo(req.getBase().getEvseNo());
        startRequest.setPlugId(req.getBase().getPlugIdx());
        startRequest.setStartType(OrderStartType.valueOf(orderData.getStartType()));
        startRequest.setStartResult(req.getOrderStartResult());

        // 费用抵扣方式
        startRequest.setCalcType(CalcType.valueOf((byte) req.getFeeDM()));

        if (startRequest.getStartType() == OrderStartType.ONLINE_VIN) {
            startRequest.setVin(orderData.getAccount());
            startRequest.setAccountNo(orderData.getAccount());
        } else {
            startRequest.setAccountNo(orderData.getAccount());
        }

        startRequest.setStartTime(req.getStartTime());
        startRequest.setSoc(req.getSoc());
        startRequest.setStopMode(req.getStopMode());
        startRequest.setOrderNo(orderData.getOrderNo());//需要传到云端的订单号
        if (req.getInsulation() != null) {
            OrderStartData.Insulation insulation = new OrderStartData.Insulation();
            insulation.setResult(req.getInsulation().getResult());
            insulation.setNegative(req.getInsulation().getNegative());
            insulation.setPositive(req.getInsulation().getPositive());
            insulation.setVoltage(req.getInsulation().getVoltage());
            startRequest.setInsulation(insulation);
        }
        startRequest.setBms(req.getBms());
        startRequest.setBattery(req.getBattery());
        return startRequest;
    }

}
