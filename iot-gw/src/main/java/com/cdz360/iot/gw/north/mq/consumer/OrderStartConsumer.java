package com.cdz360.iot.gw.north.mq.consumer;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.ds.EvseOrderRepository;
import com.cdz360.iot.gw.config.IotConfigProperties;
import com.cdz360.iot.gw.core.biz.OrderBizService;
import com.cdz360.iot.gw.model.type.OrderStopMode;
import com.cdz360.iot.gw.north.model.ChargeStartData;
import com.cdz360.iot.gw.north.model.OrderData;
import com.cdz360.iot.gw.north.mq.model.MqChargeStartMsg;
import com.cdz360.iot.gw.north.mq.model.MqMsgBase;
import com.cdz360.iot.gw.north.mq.model.MqStartChargeData;
import com.cdz360.iot.gw.util.DecimalUtils;
import com.cdz360.iot.gw.util.OrderNoGenerator;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OrderStartConsumer implements MqttConsumer {

    /**
     * 启动充电保证金
     */
    @Autowired
    private IotConfigProperties iotConfigProperties;

    @Autowired
    private EvseOrderRepository evseOrderRepository;

    @Autowired
    private OrderBizService chargeProcess;

    @Override
    public Integer method() {
        return IotGwCmdType2.CE_CHARGE_START.getCode();
    }

    @Override
    public void consume(String traceId, JsonNode message) {
        log.info("[{}] 云端开启订单 msg: {}", traceId, message);
        //{"seq":"5578F2E74E2E6C9","evseNo":"************","plugId":1,"cmd":"CHARGE_START","data":{"orderNo":"************","evseId":"************","plugId":1,"supply":"UNKNOWN","balance":200000,"startType":50,"accountNo":"***********","amount":2.0,"seq":null,"method":"ORDER","cmd":null,"stopMode":{"type":"FULL","amount":0,"kwh":0,"time":0},"defaultPayType":1},"n":"GWNO1911050007CA","v":null}
        try {
            MqMsgBase<MqChargeStartMsg> msg = JsonUtils.fromJson(message, new TypeReference<MqMsgBase<MqChargeStartMsg>>() {
            });
            MqChargeStartMsg startMsg = msg.getData();

            OrderData orderData = mapStartOrder(msg.getSeq(), startMsg);
            BigDecimal startOrderDeposit = this.iotConfigProperties.getStartOrderDeposit();
            if (DecimalUtils.gtZero(startOrderDeposit)) {
                BigDecimal amount = orderData.getAmount().subtract(startOrderDeposit);
                if (DecimalUtils.gtZero(amount)) {
                    orderData.setAmount(amount);
                    log.info("[{}] 扣除保证金 {} 后，可充电金额变更为 {}", traceId,
                        startOrderDeposit, orderData.getAmount());
                }
            }
            evseOrderRepository.addOrder(orderData);

            String evseNo = msg.getData().getEvseNo();

            // 下发开始充电
            ChargeStartData startData = new ChargeStartData();
            BeanUtils.copyProperties(msg.getData(), startData);
            startData.setSeqNo(msg.getSeq());
            startData.setEvseNo(evseNo);
            startData.setTotalAmount(msg.getData().getTotalAmount());
            startData.setFrozenAmount(orderData.getAmount());   // 冻结金额
            startData.setStopCode(startMsg.getOrderStopCode());
            startData.setCarNo(startMsg.getCarNo());
            startData.setPower(startMsg.getPower());
            startData.setSoc(startMsg.getSoc());
            startData.setPrice(startMsg.getPrice());

            int seqNo = SeqGeneratorUtil.newIntegerId(evseNo);//这个是下发给桩的序列号

            MqStartChargeData mqStartChargeData = new MqStartChargeData();
            mqStartChargeData.setChargeStartData(startData);
            mqStartChargeData.setSeqNoToEvse(seqNo);

            chargeProcess.createOrderFromCloud(traceId, mqStartChargeData);
        } catch (Exception e) {
            log.error("[{}] 处理云端发起充电失败。 msg: {}", traceId, message, e);
        }
    }


    private OrderData mapStartOrder(String seq, MqChargeStartMsg startMsg) {
        // {"orderNo":"************","evseId":"************","plugId":1,"supply":"DC","balance":1000023,"startType":50,"accountNo":"***********","amount":20000,"seq":"46FE321CC17662","method":"ORDER","cmd":"START","stopMode":{"type":"FULL","amount":0,"kwh":0,"time":0}}
        // {"orderNo":"************","evseId":"************","plugId":2,"supply":"DC","balance":********,"startType":33,"accountNo":null,"amount":********,"seq":"B8E4DBDF18ED5","method":"ORDER","cmd":"START","stopMode":{"type":"FULL","amount":0,"kwh":0,"time":0}}
        OrderData data = new OrderData();
        data.setSeq(seq);
        data.setBalance(startMsg.getTotalAmount());
        data.setAmount(startMsg.getFrozenAmount());
        String orderNo = startMsg.getOrderNo();
        data.setOrderNo(orderNo);
        data.setTransId(OrderNoGenerator.orderNo2TransId(orderNo));
        data.setStatus(0x00);
        data.setStartType(startMsg.getStartType().getCode());
        data.setAccountType(startMsg.getAccountType());

        OrderStopMode stopMode = startMsg.getStopMode();
        data.setStopMode(stopMode);

        data.setStopCode(startMsg.getOrderStopCode());
        data.setCarNo(startMsg.getCarNo());
        data.setPower(startMsg.getPower());
        data.setPrice(startMsg.getPrice());

        // 桩/枪信息
        data.setEvseNo(startMsg.getEvseNo());
        data.setIdx(startMsg.getPlugId());

        return data;
    }
}
