package com.cdz360.iot.gw.biz.ds;

import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.biz.CacheManagerServiceImpl;
import com.cdz360.iot.gw.biz.UpgradeProcess;
import com.cdz360.iot.gw.model.EvseContext;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.GwPlugVo;
import com.cdz360.iot.gw.model.base.CloudCommonResponse;
import com.cdz360.iot.gw.model.connection.PlugConnection;
import com.cdz360.iot.gw.model.evse.protocol.EvseRegisterUp;
import com.cdz360.iot.gw.model.evse.protocol.EvseRegisterUp.AcPlugInfo;
import com.cdz360.iot.gw.model.evse.protocol.EvseRegisterUp.DcPlugInfo;
import com.cdz360.iot.gw.model.type.EvseBrand;
import com.cdz360.iot.gw.north.model.GetEvseOpInfo;
import com.cdz360.iot.gw.north.server.EvseService;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 桩信息的缓存仓库
 */
@Slf4j
@Service
public class EvseRepository {
    //private final static Logger logger = LoggerFactory.getLogger(EvseRepository.class);

    private static final int HB_SEQ_STEP = 8; // 报文序列号步进值

    @Autowired
    private PlugCache plugCache;

    @Autowired
    private UpgradeProcess upgradeProcess;

    @Autowired
    private EvseService evseService;

    @Autowired
    private CacheManagerServiceImpl cacheManagerService;

    @Value("${cdz360.timer.plug-idle-timeout:60}") // 60 min
    private long plugIdleTimeout;

    private void updateEvseAndPlugCache(String traceId, GwEvseVo evse, EvseRegisterUp evseReq) {
        // 初始化枪数据
        Integer plugNum = evse.getPlugNum();
        List<GwPlugVo> plugs = new ArrayList<>();
        List<DcPlugInfo> dcPlugs = new ArrayList<>();
        List<AcPlugInfo> acPlugs = new ArrayList<>();

        for (int i = 1; i <= plugNum; i++) {
            GwPlugVo plug = new GwPlugVo().setMsgStatus(0xFF);
            plug.setStatus(PlugStatus.OFFLINE)
                .setEvseNo(evse.getEvseNo())
                .setSupply(evse.getSupplyType())
                .setIdx(i)
                .setGwno(evse.getGwno());
            plugs.add(plug);

            if (SupplyType.DC.equals(evseReq.getSupplyType())) {
                DcPlugInfo plugInfo = new DcPlugInfo();
                plugInfo.setPlugIdx(i);
                dcPlugs.add(plugInfo);
            } else {
                AcPlugInfo plugInfo = new AcPlugInfo();
                plugInfo.setPlugIdx(i);
                acPlugs.add(plugInfo);
            }
        }

        if (SupplyType.DC.equals(evseReq.getSupplyType())) {
            evseReq.setDcPlugs(dcPlugs);
        } else {
            evseReq.setAcPlugs(acPlugs);
        }

        // 桩上线需要初始化的数据
        evse.setProtocolVer(evseReq.getProtocolVersion());
        evse.setStatus(EvseStatus.ERROR); // 根据技术文档，初始状态改成ERROR
        evse.setBooting(true);
        evse.setPlugs(plugs);

        // 更新缓存数据
//        cacheManagerService.evseOnline(evse);

        cacheManagerService.updateAndSync2OtherCache(evse);
        log.info("[{} {}] 桩上线数据同步", traceId, evse.getEvseNo());
    }

    /**
     * 桩上线, 初始化桩枪信息
     * <p> 以下几种情况触发桩上线 </p>
     * <p> 1、桩在网关注册时 </p>
     *
     * @param evse
     */
    public void onlineEvse(String traceId, GwEvseVo evse, EvseRegisterUp evseReq) {
        log.debug("[{} {}] 桩上线. evse: {}", traceId, evse.getEvseNo(), evse);
        // 更新缓存数据
        this.updateEvseAndPlugCache(traceId, evse, evseReq);

        // 桩上线
        Mono.delay(Duration.ofMinutes(1))
            .subscribe(x -> this.upgradeProcess.evseReup(traceId, evse));
    }

    /**
     * 枪头全部上线后调用该接口.
     *
     * @param evseNo
     */
    public void evsePostBoot(String evseNo) {
        GwEvseVo evse = this.getEvse(evseNo);
        evse.setBooting(false);
        cacheManagerService.updateAndSync2OtherCache(evse);
    }

    /**
     * 桩离线 以下几种触发桩离线 1、桩的心跳检测不到 2、
     *
     * @param evse
     */
    public void offlineEvse(String tid, GwEvseVo evse) {
        // 调用方已经查询出对象，暂时注释，注意数据原子性
        if (evse != null) {
            evse.setStatus(EvseStatus.OFFLINE);

            // 枪对应状态也需要调整为离线
            evse.getPlugs().forEach(plug -> {
                plug.setMsgStatus(0xFF)
                    .setStatus(PlugStatus.OFFLINE);
            });

            log.debug("[{} {}] 桩下线. evse: {}", tid, evse.getEvseNo(), evse);
            cacheManagerService.updateAndSync2OtherCache(evse);
        }
    }

    /**
     * 缓存枪信息 以下几种情况触发 1、接收桩的心跳
     *
     * @return true: 数据有更新； false: 数据没有更新
     */
    public boolean updateGun(EvseContext ctx, GwPlugVo newGun) {
        log.info("[{} {}] 确认枪状态是否更新: newGun = {}", ctx.getTraceId(), ctx.getEvseNo(),
            JsonUtils.toJsonString(newGun));

        if (newGun == null) {
            log.error("[{} {}] 无法修改枪状态。newGun is null.", ctx.getTraceId(), ctx.getEvseNo());
            return false;
        }

        PlugConnection connection = plugCache.get(newGun.getEvseNo(), newGun.getIdx());
        if (connection != null) {
            Date date = connection.getLastReportTime();

            //上次上报时间超过一个小时时，强制上报枪状态
            if (date != null && date.toInstant().plusMillis(plugIdleTimeout * 1000 * 60)
                .isBefore(Instant.now())) {
                ctx.getEvse()
                    .setStatus(this.genEvseStatus(ctx.getEvse().getPlugs(), newGun));//桩的状态依赖于枪的状态
                log.debug("[{} {}] 超过设定时间间隙，强行上报状态信息。status: {}, evseCache: {}",
                    ctx.getTraceId(), ctx.getEvseNo(), newGun.getStatus(), ctx.getEvse());
                // GwEvseVo evseVo =
                cacheManagerService.updateAndSync2OtherCache(ctx, newGun);
                //ctx.setEvse(evseVo);
                return true;
            }
        }

        // 获取缓存中枪数据
        Optional<GwPlugVo> plugCache = cacheManagerService.getPlugCache(newGun.getEvseNo(),
            newGun.getIdx());
        if (plugCache.isPresent()) { // 缓存中存在枪
            GwPlugVo gwPlugVo = plugCache.get();

            log.debug("[{} {}] plugCache: {}", ctx.getTraceId(), ctx.getEvseNo(),
                JsonUtils.toJsonString(gwPlugVo));
            ctx.getEvse()
                .setStatus(this.genEvseStatus(ctx.getEvse().getPlugs(), newGun));//桩的状态依赖于枪的状态
            if (this.needUp(gwPlugVo, newGun)) {
                log.info("[{} {}] 枪头状态变更上报状态信息: plug = {}", ctx.getTraceId(),
                    ctx.getEvseNo(), JsonUtils.toJsonString(newGun));
                // 状态变更才同步到 redis
                cacheManagerService.updateAndSync2OtherCache(ctx, newGun);
                return true;
            } else {
                // 需要将订单号置空的枪头状态
                List<PlugStatus> statusList = List.of(PlugStatus.IDLE, PlugStatus.CONNECT);
                if (statusList.contains(newGun.getStatus())) {
                    newGun.setOrderNo("");
                }

                log.info("[{} {}] 状态不变更仅仅更新本地缓存: plug = {}", ctx.getTraceId(),
                    ctx.getEvseNo(), JsonUtils.toJsonString(newGun));
                // 状态不变更仅仅更新本地缓存
                GwPlugVo pair = cacheManagerService.updateLocal(ctx, newGun);
                return false;
            }

        } else {
            log.info("[{} {}] 缓存中没有枪头信息: new status = {}", ctx.getTraceId(),
                ctx.getEvseNo(), newGun.getStatus());

            ctx.getEvse().getPlugs().add(newGun);
            ctx.getEvse()
                .setStatus(this.genEvseStatus(ctx.getEvse().getPlugs(), newGun));//桩的状态依赖于枪的状态
            //GwEvseVo evseVo =
            cacheManagerService.updateAndSync2OtherCache(ctx, newGun);
            //ctx.setEvse(evseVo);
            return true;
        }

    }

    /**
     * 立即上报条件
     *
     * @param oldPlug
     * @param newPlug
     * @return
     */
    private boolean needUp(GwPlugVo oldPlug, GwPlugVo newPlug) {
        PlugStatus oldStatus = oldPlug.getStatus();
        if (null == oldStatus || !oldStatus.equals(newPlug.getStatus())) {
            if (PlugStatus.IDLE.equals(oldStatus) && PlugStatus.CONNECT.equals(
                newPlug.getStatus())) {
                newPlug.setOrderNo("");
            }
            return true;
        }

        // 故障码比较
        if (null != oldPlug.getErrorCode() || null != newPlug.getErrorCode()) {
            if (null == oldPlug.getErrorCode()) {
                return true;
            }

            if (null == newPlug.getErrorCode()) {
                return true;
            }

            if (!oldPlug.getErrorCode().equals(newPlug.getErrorCode())) {
                return true;
            }
        }

        // 告警码
        if (null != oldPlug.getAlertCode() || null != newPlug.getAlertCode()) {
            if (null == oldPlug.getAlertCode()) {
                return true;
            }

            if (null == newPlug.getAlertCode()) {
                return true;
            }

            if (!oldPlug.getAlertCode().equals(newPlug.getAlertCode())) {
                return true;
            }
        }

        // 充电订单号变更
        if (!StringUtils.isEmpty(oldPlug.getOrderNo()) && StringUtils.isEmpty(
            newPlug.getOrderNo())) {
            newPlug.setOrderNo("");
            return true;
        }

        return false;
    }

    /**
     * 根据桩号从缓存中取桩信息
     *
     * @param evseNo
     * @return
     */
    public GwEvseVo getEvse(String evseNo) {
        log.trace("获取桩缓存中信息: evseNo: {}", evseNo);
        Optional<GwEvseVo> cache = cacheManagerService.getEvseCache(evseNo);
        GwEvseVo ret = cache.orElse(null);
        if (ret == null) {
            log.info("获取缓存桩信息失败. evseNo = {}", evseNo);
        } else {
            log.info("获取结果: evseNo = {}, evseStatus = {}", ret.getEvseNo(), ret.getStatus());

            if (!ret.opInfoValidate()) {
                ret = this.fetechEvseOpInfo(ret);
                cacheManagerService.updateLocal(ret);
            }
        }
        return ret;
    }

    private GwEvseVo fetechEvseOpInfo(GwEvseVo evseVo) {
        GetEvseOpInfo opInfoReq = new GetEvseOpInfo();
        opInfoReq.setEvseNo(evseVo.getEvseNo());
        return evseService.getEvseOpInfo(null, opInfoReq).map(CloudCommonResponse::getData)
            .map(opInfo -> {
                evseVo.setBrand(
                        opInfo.getBrand() == null ? null : EvseBrand.getByDesc(opInfo.getBrand()))
                    .setTimeZone(opInfo.getTimeZone())
                    .setPlugNum(opInfo.getPlugNum())
                    .setSupplyType(opInfo.getSupply());
                return evseVo;
            }).block();
    }

    public void updateEvsePrice(@Nonnull String evseNo, @Nonnull ChargePriceVo priceVo) {
        Optional<GwEvseVo> cache = cacheManagerService.getEvseCache(evseNo);
        if (cache.isEmpty()) {
            log.error("桩不存在 evseNo: {}", evseNo);
            return;
        }
        GwEvseVo gwEvseVo = cache.get();
        gwEvseVo.setPrice(priceVo)
            .setPriceCode(priceVo.getId());
        cacheManagerService.updateAndSync2OtherCache(gwEvseVo);
    }

    /**
     * 根据枪生成桩的状态
     *
     * @param guns
     * @return
     */
    public EvseStatus genEvseStatus(List<GwPlugVo> guns, GwPlugVo curPlug) {
//        Map<PlugStatus, List<GwPlugVo>> map = guns.stream().peek(plugVo -> {
//            if (null != curPlug &&
//                    curPlug.getIdx() == plugVo.getIdx()) {
//                plugVo.setStatus(curPlug.getStatus());
//            }
//        }).collect(Collectors.groupingBy(GwPlugVo::getStatus));

        Map<PlugStatus, Integer> map = new HashMap<>();
        for (GwPlugVo plugVo : guns) {
            if (null != curPlug && curPlug.getIdx() == plugVo.getIdx()) {
                int cnt = map.getOrDefault(curPlug.getStatus(), 0) + 1;
                map.put(curPlug.getStatus(), cnt);
            } else {
                int cnt = map.getOrDefault(plugVo.getStatus(), 0) + 1;
                map.put(plugVo.getStatus(), cnt);
            }
        }

        if (map.containsKey(PlugStatus.ERROR)) {
            return EvseStatus.ERROR;
        }

        if (map.containsKey(PlugStatus.BUSY)) {
            return EvseStatus.BUSY;
        }

        if (map.containsKey(PlugStatus.RECHARGE_END)) {
            return EvseStatus.BUSY;//充电完成，枪仍然插在车上按照BUSY对待
        }

        if (map.containsKey(PlugStatus.CONNECT)) {
            return EvseStatus.CONNECT;
        }

        if (map.containsKey(PlugStatus.OFFLINE) && map.get(PlugStatus.OFFLINE) < guns.size()) {
            return EvseStatus.ERROR;
        }

        if (map.containsKey(PlugStatus.OFFLINE) && map.get(PlugStatus.OFFLINE) == guns.size()) {
            return EvseStatus.OFFLINE;
        }

        if (map.containsKey(PlugStatus.IDLE) && map.get(PlugStatus.IDLE) == guns.size()) {
            return EvseStatus.IDLE;
        }

        return EvseStatus.UNKNOWN;
    }
}
