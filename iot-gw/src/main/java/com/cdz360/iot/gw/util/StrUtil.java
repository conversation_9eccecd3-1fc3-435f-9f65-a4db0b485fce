package com.cdz360.iot.gw.util;

import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;

public class StrUtil {

    public static byte[] hexStrToByteArray(String str) {
        // 对输入值进行规范化整理
        str = str.trim().replace(" ", "").toUpperCase(Locale.US);

        str = str.length() % 2 == 0 ? str : "0" + str; // 补全

        // 处理值初始化
        int m = 0, n = 0;
        int iLen = str.length() / 2; // 计算长度
        byte[] ret = new byte[iLen]; // 分配存储空间

        for (int i = 0; i < iLen; i++) {
            m = i * 2 + 1;
            n = m + 1;
            ret[i] = (byte) (Integer.decode("0x" + str.substring(i * 2, m) + str.substring(m, n)) & 0xFF);
        }

        return ret;
    }


    /**
     * 十六进制字符串转为BCD码
     *
     * @param data
     * @param src
     * @param offset
     */
    public static void hexStr2BCD(byte[] data, String src, int offset) {
        int len = (src.length() / 2);
        char[] c = src.toCharArray();
        int idx = offset;
        for (int i = 0; i < len; i++) {
            int pos = i * 2;

            data[idx++] = (byte) (toByte(c[pos]) << 4 | toByte(c[pos + 1]));
        }
    }

    private static byte toByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }

    /**
     * 去除字符串中的汉字
     *
     * @param target
     * @return
     */
    public static String rmChineseChar(String target) {
        if (StringUtils.isBlank(target)) {
            return "";
        }

        String regex ="[\u4e00-\u9fa5]";
        Pattern pat = Pattern.compile(regex);
        Matcher mat = pat.matcher(target);
        return mat.replaceAll("");
    }
}
