package com.cdz360.iot.gw.south.server;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.ChargePriceCategory;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.vo.ChargePriceItem;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.PlugNoUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.biz.UpgradeQueueManager;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.biz.ds.EvseSocketCache;
import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.core.biz.OrderBizService;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.GwPlugVo;
import com.cdz360.iot.gw.model.base.SocketInfo;
import com.cdz360.iot.gw.model.connection.EvseConnection;
import com.cdz360.iot.gw.model.evse.protocol.CardModifyDown;
import com.cdz360.iot.gw.model.evse.protocol.CardQueryDown;
import com.cdz360.iot.gw.model.evse.protocol.Charge4RemoteRes;
import com.cdz360.iot.gw.model.evse.protocol.ChargeStopDown;
import com.cdz360.iot.gw.model.evse.protocol.EvseRebootDown;
import com.cdz360.iot.gw.model.evse.protocol.EvseUpgradeDown;
import com.cdz360.iot.gw.model.evse.rcd.RcdBaseConfVo;
import com.cdz360.iot.gw.model.evse.rcd.RcdQrCodeReqVo;
import com.cdz360.iot.gw.model.evse.rcd.RcdRateReqVo;
import com.cdz360.iot.gw.model.evse.rcd.RcdRemoteStartReqVo;
import com.cdz360.iot.gw.model.type.ChargeMode;
import com.cdz360.iot.gw.model.type.EvseBrand;
import com.cdz360.iot.gw.north.mq.model.EvseVersion;
import com.cdz360.iot.gw.util.OcppZonedDateTime;
import com.cdz360.iot.gw.util.OrderNoGenerator;
import com.cdz360.iot.gw.util.URLUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import eu.chargetime.ocpp.JSONServer;
import eu.chargetime.ocpp.feature.profile.ServerCoreProfile;
import eu.chargetime.ocpp.model.core.AuthorizationStatus;
import eu.chargetime.ocpp.model.core.ChangeConfigurationConfirmation;
import eu.chargetime.ocpp.model.core.ChangeConfigurationRequest;
import eu.chargetime.ocpp.model.core.ClearCacheConfirmation;
import eu.chargetime.ocpp.model.core.ClearCacheRequest;
import eu.chargetime.ocpp.model.core.ClearCacheStatus;
import eu.chargetime.ocpp.model.core.ConfigurationStatus;
import eu.chargetime.ocpp.model.core.DataTransferConfirmation;
import eu.chargetime.ocpp.model.core.DataTransferRequest;
import eu.chargetime.ocpp.model.core.DataTransferStatus;
import eu.chargetime.ocpp.model.core.GetConfigurationConfirmation;
import eu.chargetime.ocpp.model.core.GetConfigurationRequest;
import eu.chargetime.ocpp.model.core.IdTagInfo;
import eu.chargetime.ocpp.model.core.KeyValueType;
import eu.chargetime.ocpp.model.core.RemoteStartStopStatus;
import eu.chargetime.ocpp.model.core.RemoteStartTransactionConfirmation;
import eu.chargetime.ocpp.model.core.RemoteStartTransactionRequest;
import eu.chargetime.ocpp.model.core.RemoteStopTransactionConfirmation;
import eu.chargetime.ocpp.model.core.RemoteStopTransactionRequest;
import eu.chargetime.ocpp.model.core.ResetRequest;
import eu.chargetime.ocpp.model.core.ResetType;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import eu.chargetime.ocpp.model.dc.type.DataTransferMessage;
import eu.chargetime.ocpp.model.firmware.UpdateFirmwareRequest;
import eu.chargetime.ocpp.model.localauthlist.AuthorizationData;
import eu.chargetime.ocpp.model.localauthlist.GetLocalListVersionConfirmation;
import eu.chargetime.ocpp.model.localauthlist.GetLocalListVersionRequest;
import eu.chargetime.ocpp.model.localauthlist.SendLocalListConfirmation;
import eu.chargetime.ocpp.model.localauthlist.SendLocalListRequest;
import eu.chargetime.ocpp.model.localauthlist.UpdateStatus;
import eu.chargetime.ocpp.model.localauthlist.UpdateType;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import javax.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class JsonServerImpl {

    @Autowired
    private JSONServer server;

    @Autowired
    private ServerCoreProfile serverCoreProfile;

    @Autowired
    private SocketRepository socketRepository;

    @Autowired
    private EvseSocketCache evseSocketRepository;

    @Autowired
    private EvseRepository evseRepository;

    @Autowired
    private WhiteCardCache whiteCardCache;

    @Autowired
    private OrderBizService orderBizService;

    @Autowired
    private UpgradeQueueManager upgradeQueueManager;

    private UUID getSessionIndexOrThrow(EvseMsgBase base) {
        String tid = base.getTid();
        String evseNo = base.getEvseNo();
        EvseConnection evseConnection = evseSocketRepository.get(evseNo);
        UUID sessionIndex = Optional.ofNullable(evseConnection)
            .map(EvseConnection::getClientAddress).map(e -> socketRepository.get(e))
            .map(SocketInfo::getSessionIndex).orElse(null);
        if (sessionIndex == null) {
            log.error("[{} {}] evseConnection is null.", tid, evseNo);
            throw new DcServiceException("桩已失去连接");
        }
        base.setChKey(evseConnection.getClientAddress());
        return sessionIndex;
    }

    // 发送清除缓存请求的示例方法
    public boolean sendClearCacheRequest(String tid, String evseNo) {
        EvseMsgBase base = new EvseMsgBase(tid, "");
        base.setEvseNo(evseNo);
        UUID sessionIndex = getSessionIndexOrThrow(base);

        ClearCacheRequest request = serverCoreProfile.createClearCacheRequest();
        request.setBase(base);
        try {
            CompletableFuture<Boolean> future = new CompletableFuture<>();
            server.send(sessionIndex, request).whenComplete((confirmation, throwable) -> {
                log.info("[{}] sendClearCacheRequest confirmation: {}", evseNo, confirmation);
                if (throwable != null) {
                    future.completeExceptionally(throwable);
                } else {
                    ClearCacheConfirmation conf = (ClearCacheConfirmation) confirmation;
                    future.complete(ClearCacheStatus.Accepted.equals(conf.getStatus()));
                }
            });
            // 设置适当的超时时间，比如5秒
            return future.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("sendClearCacheRequest error: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送平台发起充电请求
     *
     * @param dMsg
     * @return
     */
    public boolean sendRemoteStartTrans(Charge4RemoteRes dMsg) {
        EvseMsgBase base = dMsg.getBase();
        String tid = base.getTid();
        String evseNo = base.getEvseNo();
        int plugIdx = base.getPlugIdx();

        UUID sessionIndex = getSessionIndexOrThrow(base);

        GwEvseVo evse = evseRepository.getEvse(evseNo);
        if (EvseBrand.RCD.equals(evse.getBrand())) {
            return this.sendRcdRemoteStartDataTransfer(dMsg, sessionIndex, evse);
        }

        String idTag = dMsg.getOrderNo();
        RemoteStartTransactionRequest request = serverCoreProfile.createRemoteStartTransactionRequest(
            idTag);
        request.setBase(base);
        request.setConnectorId(plugIdx);
        try {
            CompletableFuture<Boolean> future = new CompletableFuture<>();
            // 异步操作
            server.send(sessionIndex, request).whenComplete((confirmation, throwable) -> {
                log.info("[{} {}] sendRemoteStartTrans conf: {}", tid, evseNo, confirmation);
                if (throwable != null) {
                    future.completeExceptionally(throwable);
                } else {
                    RemoteStartTransactionConfirmation conf = (RemoteStartTransactionConfirmation) confirmation;
                    future.complete(RemoteStartStopStatus.Accepted.equals(conf.getStatus()));
                }
            });
            // 设置适当的超时时间，比如5秒
            return future.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("[{} {}] sendRemoteStartTrans error: {}", tid, evseNo, e.getMessage(), e);
            return false;
        }
    }

    /**
     * RCD桩自定义远程开启充电
     *
     * @param dMsg
     * @param sessionIndex
     * @param evse
     * @return
     */
    public boolean sendRcdRemoteStartDataTransfer(Charge4RemoteRes dMsg, UUID sessionIndex,
        GwEvseVo evse) {
        EvseMsgBase base = dMsg.getBase();
        String tid = base.getTid();
        String evseNo = base.getEvseNo();

        if (ChargeMode.FULL.equals(dMsg.getChargeMode())) {
            if (DecimalUtils.gtZero(dMsg.getAmount())) {
                dMsg.setChargeMode(ChargeMode.AMOUNT);
            }
        }

        RcdRemoteStartReqVo startReqVo = new RcdRemoteStartReqVo();
        startReqVo.setAccount(dMsg.getOrderNo())
            .setChargeMode(1)
            .setConnector(dMsg.getBase().getPlugIdx())
            .setSerial(OrderNoGenerator.orderNo2TransId(dMsg.getOrderNo()))
            .setStopPassword(dMsg.getStopCode())
            .setType(1);

        if (SupplyType.AC.equals(evse.getSupplyType())) {
            startReqVo.setBmsSelect(0);
        } else {
            startReqVo.setBmsSelect(1);
        }

        switch (dMsg.getChargeMode()) {
            case AMOUNT:
                startReqVo.setChargeArg(2);
                startReqVo.setChargeType(dMsg.getAmount().movePointRight(2).intValue());
                break;
            case KWH:
                startReqVo.setChargeArg(1);
                break;
            case TIME:
                startReqVo.setChargeArg(3);
                break;
            case FULL:
            default:
                startReqVo.setChargeArg(0);
                startReqVo.setChargeType(0);
                break;
        }

        DataTransferRequest request = serverCoreProfile.createDataTransferRequest(
            EvseBrand.RCD.getVendorId());
        request.setMessageId(DataTransferMessage.REMOTE_START_REQ.getStr());
        request.setData(JsonUtils.toJsonString(startReqVo));
        request.setBase(base);

        try {
            CompletableFuture<Boolean> future = new CompletableFuture<>();
            // 异步操作
            server.send(sessionIndex, request).whenComplete((confirmation, throwable) -> {
                log.info("[{} {}] sendRcdRemoteStartDataTransfer conf: {}", tid, evseNo,
                    confirmation);
                if (throwable != null) {
                    future.completeExceptionally(throwable);
                } else {
                    boolean result = false;
                    DataTransferConfirmation conf = (DataTransferConfirmation) confirmation;
                    if (DataTransferStatus.Accepted.equals(conf.getStatus())) {
                        RcdBaseConfVo confDataVo = JsonUtils.fromJson(conf.getData(),
                            RcdBaseConfVo.class);
                        result = DataTransferMessage.REMOTE_START_CONF.getStr()
                            .equals(confDataVo.getMessageId())
                            && confDataVo.getResult() == IotGwConstants.SUCCESS;
                    }
                    future.complete(result);
                }
            });
            // 设置适当的超时时间，比如5秒
            return future.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("[{} {}] sendRcdRemoteStartDataTransfer error: {}", tid, evseNo,
                e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送平台停止充电请求
     *
     * @param dMsg
     * @return
     */
    public boolean sendRemoteStopTrans(ChargeStopDown dMsg, Integer transId) {
        EvseMsgBase base = dMsg.getBase();
        String tid = base.getTid();
        String evseNo = base.getEvseNo();
        int plugIdx = base.getPlugIdx();

        UUID sessionIndex = getSessionIndexOrThrow(base);

        RemoteStopTransactionRequest request = serverCoreProfile.createRemoteStopTransactionRequest(
            transId);
        request.setBase(base);
        try {
            CompletableFuture<Boolean> future = new CompletableFuture<>();
            // 异步操作
            server.send(sessionIndex, request).whenComplete((confirmation, throwable) -> {
                log.info("[{} {}] sendRemoteStopTrans conf: {}", tid, evseNo, confirmation);
                if (throwable != null) {
                    future.completeExceptionally(throwable);
                } else {
                    RemoteStopTransactionConfirmation conf = (RemoteStopTransactionConfirmation) confirmation;
                    future.complete(RemoteStartStopStatus.Accepted.equals(conf.getStatus()));
                }
            });
            // 设置适当的超时时间，比如5秒
            return future.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("[{} {}] sendRemoteStopTrans error: {}", tid, evseNo, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据桩品牌判断计费信息是否下发到桩
     *
     * @param base
     * @param price
     * @return
     */
    public Optional<Boolean> sendRcdEvsePriceByConditions(EvseMsgBase base,
        @Nullable ChargePriceVo price) {
        String tid = base.getTid();
        String evseNo = base.getEvseNo();

        GwEvseVo evse = evseRepository.getEvse(evseNo);
        // TODO: 2025/4/21 WZFIX 待删除日志
        log.info("[{} {}] sendRcdEvsePrice. evse: {}", tid, evseNo, evse);
        EvseBrand brand = evse.getBrand();
        if (!EvseBrand.RCD.equals(brand)) {
            log.info("[{} {}] 当前桩品牌无需下发计费信息. brand: {}", tid, evseNo, brand);
            return Optional.empty();
        }

        UUID sessionIndex = getSessionIndexOrThrow(base);
        if (price == null) {
            price = evse.getPrice();
        }
        if (price == null) {
            price = orderBizService.fetchEvseCloudPrice(tid, evseNo).block();
            evseRepository.updateEvsePrice(evseNo,
                price); // TODO: 2025/4/21 WZFIX 这里是否save还需斟酌，可能只是单次充电计费信息
        }

        try {
            DataTransferRequest request = serverCoreProfile.createDataTransferRequest(
                brand.getVendorId());
            request.setBase(base);
            request.setMessageId(DataTransferMessage.RATE_REQ.getStr());
            request.setData(this.generateRcdRateVoJson(price));

            CompletableFuture<Boolean> future = new CompletableFuture<>();
            // 异步操作
            server.send(sessionIndex, request).whenComplete((confirmation, throwable) -> {
                log.info("[{} {}] sendRcdEvsePrice conf: {}", tid, evseNo, confirmation);
                if (throwable != null) {
                    future.completeExceptionally(throwable);
                } else {
                    DataTransferConfirmation conf = (DataTransferConfirmation) confirmation;
                    future.complete(DataTransferStatus.Accepted.equals(conf.getStatus()));
                }
            });
            // 设置适当的超时时间，比如5秒
            return Optional.of(future.get(5, TimeUnit.SECONDS));
        } catch (Exception e) {
            log.error("[{} {}] sendRcdEvsePrice发送错误 error: {}", tid, evseNo, e.getMessage(), e);
            return Optional.of(false);
        }

    }

    private String generateRcdRateVoJson(ChargePriceVo price) throws JsonProcessingException {

        // 创建费率对象
        RcdRateReqVo rate = new RcdRateReqVo();
        rate.setPeriodNum(price.getItemList().size());

        // 按时间排序
        price.getItemList().sort(Comparator.comparing(item -> {
            String time = item.getStartTime();
            String[] parts = time.split(":");
            return LocalTime.of(Integer.parseInt(parts[0]), Integer.parseInt(parts[1]));
        }));
        for (int i = 0; i < price.getItemList().size(); i++) {
            ChargePriceItem e = price.getItemList().get(i);
            Integer tag = e.getCode();
            if (tag == ChargePriceCategory.PRICE_TAG_JIAN.getCode()) {
                rate.setSharpPrice(e.getElecPrice());
                rate.setSharpService(e.getServPrice());
            } else if (tag == ChargePriceCategory.PRICE_TAG_FENG.getCode()) {
                rate.setPeakPrice(e.getElecPrice());
                rate.setPeakService(e.getServPrice());
            } else if (tag == ChargePriceCategory.PRICE_TAG_PING.getCode()) {
                rate.setFlatPrice(e.getElecPrice());
                rate.setFlatService(e.getServPrice());
            } else if (tag == ChargePriceCategory.PRICE_TAG_GU.getCode()) {
                rate.setValleyPrice(e.getElecPrice());
                rate.setValleyService(e.getServPrice());
            }
            int index = i + 1;
            rate.getPeriodFields().put("period" + index + "Type", (tag - 1));
            rate.getPeriodFields().put("period" + index + "Start", e.getStartTime());
            rate.getPeriodFields().put("period" + index + "End", e.getEndTime());
        }

        // 转换为JSON
        ObjectMapper mapper = new ObjectMapper();
        String jsonData = mapper.writeValueAsString(rate);
        log.info("JSON数据: {}", jsonData);
        return jsonData;
    }

    /*
    public static void main(String[] args) throws JsonProcessingException {

        List<ChargePriceItem> itemList = new ArrayList<>();
        ChargePriceItem item1 = new ChargePriceItem();
        item1.setCode(ChargePriceCategory.PRICE_TAG_JIAN.getCode());
        item1.setStartTime("00:00");
        item1.setEndTime("08:00");
        item1.setElecPrice(new BigDecimal("0.5"));
        item1.setServPrice(new BigDecimal("0.1"));
        itemList.add(item1);

        ChargePriceItem item3 = new ChargePriceItem();
        item3.setCode(ChargePriceCategory.PRICE_TAG_PING.getCode());
        item3.setStartTime("12:00");
        item3.setEndTime("18:00");
        item3.setElecPrice(new BigDecimal("0.7"));
        item3.setServPrice(new BigDecimal("0.3"));
        itemList.add(item3);

        ChargePriceItem item2 = new ChargePriceItem();
        item2.setCode(ChargePriceCategory.PRICE_TAG_FENG.getCode());
        item2.setStartTime("08:00");
        item2.setEndTime("12:00");
        item2.setElecPrice(new BigDecimal("0.6"));
        item2.setServPrice(new BigDecimal("0.2"));
        itemList.add(item2);

        ChargePriceVo price = new ChargePriceVo();
        price.setItemList(itemList);

        String s = generateRcdRateVoJson(price);
        log.info("json: {}", s);
    }
    */

    public Optional<Boolean> sendQrCodeByConditions(EvseMsgBase base, String qrCodePrefix) {
        String tid = base.getTid();
        String evseNo = base.getEvseNo();

        GwEvseVo evse = evseRepository.getEvse(evseNo);
        EvseBrand brand = evse.getBrand();
        if (EvseBrand.RCD.equals(brand)) {
            return sendRcdQrCodeByConditions(base, qrCodePrefix, evse);
        } else if (EvseBrand.WINLINE.equals(brand)) {
            return sendWlQrCodeByConditions(base, qrCodePrefix, evse);
        } else {
            log.info("[{} {}] 当前桩品牌无需下发二维码. brand: {}", tid, evseNo, brand);
            return Optional.empty();
        }
    }

    /**
     * 根据桩品牌判断二维码是否下发到桩
     *
     * @param base
     * @param qrCodePrefix
     * @return
     */
    private Optional<Boolean> sendRcdQrCodeByConditions(EvseMsgBase base, String qrCodePrefix,
        GwEvseVo evse) {
        String tid = base.getTid();
        String evseNo = base.getEvseNo();

        EvseBrand brand = evse.getBrand();
        if (!EvseBrand.RCD.equals(brand)) {
            log.info("[{} {}] 当前桩品牌无需下发二维码. brand: {}", tid, evseNo, brand);
            return Optional.empty();
        }

        UUID sessionIndex = getSessionIndexOrThrow(base);

        // TODO: 2025/4/22 WZFIX 待删除日志
        log.info("[{} {}] sendRcdQrCodeByConditions. evse.getPlugs(): {}", tid, evseNo,
            JsonUtils.toJsonString(evse.getPlugs()));

        try {
            boolean result = true;
            DataTransferRequest request = serverCoreProfile.createDataTransferRequest(
                brand.getVendorId());
            request.setBase(base);
            request.setMessageId(DataTransferMessage.QRCODE_REQ.getStr());

            for (GwPlugVo plug : evse.getPlugs()) {
                RcdQrCodeReqVo reqVo = new RcdQrCodeReqVo();
                reqVo.setConnector_id(plug.getIdx());
                reqVo.setUrl(URLUtil.concatenateURL(qrCodePrefix,
                    PlugNoUtils.formatPlugNo(plug.getEvseNo(), plug.getIdx())).toString());
                this.rcdSpecialAdaptation(reqVo);
                request.setData(JsonUtils.toJsonString(reqVo));

                // 异步操作
                CompletableFuture<Boolean> future = new CompletableFuture<>();
                server.send(sessionIndex, request).whenComplete((confirmation, throwable) -> {
                    log.info("[{} {}] sendRcdQrCodeByConditions conf: {}", tid, evseNo,
                        confirmation);
                    if (throwable != null) {
                        future.completeExceptionally(throwable);
                    } else {
                        DataTransferConfirmation conf = (DataTransferConfirmation) confirmation;
                        future.complete(DataTransferStatus.Accepted.equals(conf.getStatus()));
                    }
                });
                // 设置适当的超时时间，比如5秒
                Boolean success = future.get(5, TimeUnit.SECONDS);
                log.info("[{} {}] 下发二维码到枪 第{}/{}把, result: {}", tid, evseNo, plug.getIdx(),
                    evse.getPlugs().size(), success);

                result = result && success;
            }
            return Optional.of(result);
        } catch (Exception e) {
            log.error("[{} {}] sendRcdQrCodeByConditions发送错误 error: {}", tid, evseNo,
                e.getMessage(), e);
            return Optional.of(false);
        }

    }

    /**
     * RCD桩二维码下发参数完整性适配
     *
     * @param reqVo
     */
    private void rcdSpecialAdaptation(RcdQrCodeReqVo reqVo) {
        reqVo.setEvse_id("");
        reqVo.setPrice(BigDecimal.ZERO);
        reqVo.setUnit("EUR/kWh");
    }

    private Optional<Boolean> sendWlQrCodeByConditions(EvseMsgBase base, String qrCodePrefix,
        GwEvseVo evse) {
        String tid = base.getTid();
        String evseNo = base.getEvseNo();

        UUID sessionIndex = getSessionIndexOrThrow(base);

        // TODO: 2025/4/22 WZFIX 待删除日志
        log.info("[{} {}] sendWlQrCodeByConditions. evse.getPlugs(): {}", tid, evseNo,
            JsonUtils.toJsonString(evse.getPlugs()));

        try {
            boolean result = true;

            for (GwPlugVo plug : evse.getPlugs()) {
                String key = IotGwConstants.WINLINE_CONFIG_KEY_QR_CODE_PREFIX + plug.getIdx();
                String value = URLUtil.concatenateURL(qrCodePrefix,
                    PlugNoUtils.formatPlugNo(plug.getEvseNo(), plug.getIdx()));
                ChangeConfigurationRequest request = serverCoreProfile.createChangeConfigurationRequest(
                    key, value);
                request.setBase(base);

                // 异步操作
                CompletableFuture<Boolean> future = new CompletableFuture<>();
                server.send(sessionIndex, request).whenComplete((confirmation, throwable) -> {
                    log.info("[{} {}] sendWlQrCodeByConditions conf: {}", tid, evseNo,
                        confirmation);
                    if (throwable != null) {
                        future.completeExceptionally(throwable);
                    } else {
                        ChangeConfigurationConfirmation conf = (ChangeConfigurationConfirmation) confirmation;
                        future.complete(ConfigurationStatus.Accepted.equals(conf.getStatus()));
                    }
                });
                // 设置适当的超时时间，比如5秒
                Boolean success = future.get(5, TimeUnit.SECONDS);
                log.info("[{} {}] 下发二维码到枪 第{}/{}把, result: {}", tid, evseNo, plug.getIdx(),
                    evse.getPlugs().size(), success);

                result = result && success;
            }
            return Optional.of(result);
        } catch (Exception e) {
            log.error("[{} {}] sendWlQrCodeByConditions error: {}", tid, evseNo, e.getMessage(), e);
            return Optional.of(false);
        }

    }

    public UpdateStatus sendLocalList(@Nonnull CardModifyDown modifyDown, @Nonnull String cfgVer) {
        EvseMsgBase base = modifyDown.getBase();
        String tid = base.getTid();
        String evseNo = base.getEvseNo();

        UUID sessionIndex = getSessionIndexOrThrow(base);

        Integer listVersion = this.getListVersion(tid, cfgVer);

        // 将白名单信息保存到缓存（直接保存原始的WhiteCard列表）
        whiteCardCache.saveWhiteCard(evseNo, listVersion, modifyDown.getWhiteCards());

        SendLocalListRequest request = serverCoreProfile.createSendLocalListRequest(listVersion,
            UpdateType.Full);
        // 转换为AuthorizationData[]用于发送请求
        AuthorizationData[] array = modifyDown.getWhiteCards().stream().map(e -> {
            AuthorizationData localAuthorization = new AuthorizationData(e.getCardNumber());
            IdTagInfo localIdTagInfo = new IdTagInfo(AuthorizationStatus.Accepted);
            localAuthorization.setIdTagInfo(localIdTagInfo);
            return localAuthorization;
        }).toArray(AuthorizationData[]::new);
        request.setLocalAuthorizationList(array);
        request.setBase(base);
        try {
            CompletableFuture<UpdateStatus> future = new CompletableFuture<>();
            // 异步操作
            server.send(sessionIndex, request).whenComplete((confirmation, throwable) -> {
                log.info("[{} {}] sendLocalList conf: {}", tid, evseNo, confirmation);
                if (throwable != null) {
                    future.completeExceptionally(throwable);
                } else {
                    SendLocalListConfirmation conf = (SendLocalListConfirmation) confirmation;
                    future.complete(conf.getStatus());
                }
            });
            // 设置适当的超时时间，比如5秒
            return future.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("[{} {}] sendLocalList error: {}", tid, evseNo, e.getMessage(), e);
            return UpdateStatus.Failed;
        }
    }

    private int getListVersion(String tid, String cfgVer) {
        if (cfgVer == null || cfgVer.isEmpty()) {
            return 1;
        }
        try {
            String numericPart = cfgVer.replaceAll("[^0-9]", ""); // 去除非数字字符
            return Integer.parseInt(numericPart.substring(2));
        } catch (Exception e) {
            log.error("[{}] : {}", tid, cfgVer, e);
            return 1;
        }
    }

    public Integer enquiryLocalListVersion(@Nonnull CardQueryDown queryDown) {
        EvseMsgBase base = queryDown.getBase();
        String tid = base.getTid();
        String evseNo = base.getEvseNo();

        UUID sessionIndex = getSessionIndexOrThrow(base);

        GetLocalListVersionRequest request = new GetLocalListVersionRequest();
        request.setBase(base);
        try {
            CompletableFuture<Integer> future = new CompletableFuture<>();
            // 异步操作
            server.send(sessionIndex, request).whenComplete((confirmation, throwable) -> {
                log.info("[{} {}] enquiryLocalListVersion conf: {}", tid, evseNo, confirmation);
                if (throwable != null) {
                    future.completeExceptionally(throwable);
                } else {
                    GetLocalListVersionConfirmation conf = (GetLocalListVersionConfirmation) confirmation;
                    future.complete(conf.getListVersion());
                }
            });
            // 设置适当的超时时间，比如5秒
            return future.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("[{} {}] enquiryLocalListVersion error: {}", tid, evseNo, e.getMessage(), e);
            return null;
        }
    }

    public boolean sendEvseResetReq(EvseRebootDown dMsg) {
        EvseMsgBase base = dMsg.getBase();
        String tid = base.getTid();
        String evseNo = base.getEvseNo();

        UUID sessionIndex = getSessionIndexOrThrow(base);

        ResetRequest request = serverCoreProfile.createResetRequest(ResetType.Hard);
        request.setBase(base);
        try {
            CompletableFuture<Boolean> future = new CompletableFuture<>();
            // 异步操作
            server.send(sessionIndex, request).whenComplete((confirmation, throwable) -> {
                log.info("[{} {}] sendResetReq conf: {}", tid, evseNo, confirmation);
                if (throwable != null) {
                    future.completeExceptionally(throwable);
                } else {
                    future.complete(true);
                }
            });
            // 设置适当的超时时间，比如5秒
            return future.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("[{} {}] sendResetReq error: {}", tid, evseNo, e.getMessage(), e);
            return false;
        }
    }

    public boolean sendUpdateFirmwareReq(EvseUpgradeDown dMsg) {
        EvseMsgBase base = dMsg.getBase();
        String tid = dMsg.getBase().getTid();
        String evseNo = dMsg.getBase().getEvseNo();

        UUID sessionIndex = getSessionIndexOrThrow(base);

        String pc01Url = dMsg.getPc01Url();
        if (StringUtils.isBlank(pc01Url)) {
            log.error("[{} {}] 升级文件路径为空.", tid, evseNo);
            throw new DcServiceException("升级文件路径为空");
        }

        String location = URLUtil.convertFtpUrl2RcdLocation(pc01Url, dMsg.getAccountNo(),
            dMsg.getPassword());

        GwEvseVo evse = evseRepository.getEvse(evseNo);
        ZonedDateTime nowByTz = OcppZonedDateTime.issueTime(evse.getBrand(), evse.getTimeZone());
        UpdateFirmwareRequest request = new UpdateFirmwareRequest(location, nowByTz);
        request.setBase(base);
        request.setRetries(3);
        request.setRetryInterval(300); // 5分钟
        try {
            CompletableFuture<Boolean> future = new CompletableFuture<>();
            // 异步操作
            server.send(sessionIndex, request).whenComplete((confirmation, throwable) -> {
                log.info("[{} {}] sendUpdateFirmwareReq conf: {}", tid, evseNo, confirmation);
                if (throwable != null) {
                    future.completeExceptionally(throwable);
                } else {
                    future.complete(true);
                }
            });
            // 设置适当的超时时间
            return future.get(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("[{} {}] sendUpdateFirmwareReq error: {}", tid, evseNo, e.getMessage(), e);
            return false;
        }
    }

    public boolean sendUpdateFirmwareReq2(EvseMsgBase base, EvseVersion version, String accountNo,
        String password) {
        String tid = base.getTid();
        String evseNo = base.getEvseNo();

        UUID sessionIndex = getSessionIndexOrThrow(base);

        // 判断url是否是FTP
        String location = null;
        String url = version.getUrl();
        if (url != null && url.toLowerCase().startsWith("ftp://")) {
            location = URLUtil.convertFtpUrl2RcdLocation(url, accountNo, password);
        } else {
            location = url;
        }

        GwEvseVo evse = evseRepository.getEvse(evseNo);
        ZonedDateTime nowByTz = OcppZonedDateTime.issueTime(evse.getBrand(), evse.getTimeZone());
        UpdateFirmwareRequest request = new UpdateFirmwareRequest(location, nowByTz);
        request.setBase(base);
        request.setRetries(3);
        request.setRetryInterval(300); // 5分钟
        try {
            CompletableFuture<Boolean> future = new CompletableFuture<>();
            // 异步操作
            server.send(sessionIndex, request).whenComplete((confirmation, throwable) -> {
                log.info("[{} {}] sendUpdateFirmwareReq fileName: {}, conf: {}", tid, evseNo,
                    version.getName(), confirmation);
                if (throwable != null) {
                    future.completeExceptionally(throwable);
                } else {
                    future.complete(true);
                }
            });
            // 设置适当的超时时间
            return future.get(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("[{} {}] sendUpdateFirmwareReq fileName: {}, error: {}", tid, evseNo,
                version.getName(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理下一个升级文件的方法
     * <p>这个方法需要在收到Installed状态时调用</p>
     *
     * @param evseNo 桩编号
     * @return empty: 没有更多升级, true: 成功发送下一个升级请求, false: 发送失败
     */
    public Optional<Boolean> processNextUpgrade(String evseNo) {
        if (!upgradeQueueManager.hasNext(evseNo)) {
            log.info("桩 {} 没有更多待升级的文件", evseNo);
            return Optional.empty();
        }

        UpgradeQueueManager.UpgradeQueue queue = upgradeQueueManager.getUpgradeQueue(evseNo);
        if (queue == null) {
            log.warn("桩 {} 的升级队列不存在", evseNo);
            return Optional.empty();
        }

        EvseVersion nextVersion = upgradeQueueManager.getNextVersion(evseNo);
        if (nextVersion == null) {
            log.info("桩 {} 没有下一个升级版本", evseNo);
            return Optional.empty();
        }
        // 构造升级请求
        EvseMsgBase base = new EvseMsgBase();
        base.setEvseNo(evseNo);
        base.setTid(queue.getTid());
        return Optional.of(this.sendUpdateFirmwareReq2(base, nextVersion, queue.getAccountNo(),
            queue.getPassword()));
    }

    /**
     * 读取桩配置
     *
     * @param keys
     * @return
     */
    public Optional<KeyValueType[]> sendGetConfigurationReq(EvseMsgBase base, List<String> keys) {
        String tid = base.getTid();
        String evseNo = base.getEvseNo();
        UUID sessionIndex = getSessionIndexOrThrow(base);

        GetConfigurationRequest request = serverCoreProfile.createGetConfigurationRequest();
        request.setBase(base);
        if (CollectionUtils.isNotEmpty(keys)) {
            request.setKey(keys.toArray(new String[0]));
        }

        KeyValueType[] res = null;
        try {
            CompletableFuture<KeyValueType[]> future = new CompletableFuture<>();
            // 异步操作
            server.send(sessionIndex, request).whenComplete((confirmation, throwable) -> {
                log.info("[{} {}] sendGetConfigurationReq conf: {}", tid, evseNo, confirmation);
                if (throwable != null) {
                    future.completeExceptionally(throwable);
                } else {
                    GetConfigurationConfirmation conf = (GetConfigurationConfirmation) confirmation;
                    future.complete(conf.getConfigurationKey());
                }
            });
            // 设置适当的超时时间，比如5秒
            res = future.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("[{} {}] sendGetConfigurationReq error: {}", tid, evseNo, e.getMessage(), e);
        }
        return Optional.ofNullable(res);
    }

    /**
     * 更改桩配置
     *
     * @param key
     * @param value
     * @return
     */
    public boolean sendChangeConfigurationReq(EvseMsgBase base, String key, String value) {
        String tid = base.getTid();
        String evseNo = base.getEvseNo();
        UUID sessionIndex = getSessionIndexOrThrow(base);

        ChangeConfigurationRequest request = serverCoreProfile.createChangeConfigurationRequest(key,
            value);
        request.setBase(base);
        try {
            CompletableFuture<Boolean> future = new CompletableFuture<>();
            // 异步操作
            server.send(sessionIndex, request).whenComplete((confirmation, throwable) -> {
                log.info("[{} {}] sendChangeConfigurationReq conf: {}", tid, evseNo, confirmation);
                if (throwable != null) {
                    future.completeExceptionally(throwable);
                } else {
                    ChangeConfigurationConfirmation conf = (ChangeConfigurationConfirmation) confirmation;
                    future.complete(ConfigurationStatus.Accepted.equals(conf.getStatus()));
                }
            });
            // 设置适当的超时时间，比如5秒
            return future.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("[{} {}] sendChangeConfigurationReq error: {}", tid, evseNo, e.getMessage(),
                e);
            return false;
        }
    }

}
