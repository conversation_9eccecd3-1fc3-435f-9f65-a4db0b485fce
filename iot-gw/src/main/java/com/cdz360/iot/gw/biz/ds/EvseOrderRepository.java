package com.cdz360.iot.gw.biz.ds;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.iot.gw.biz.RedisOrderCacheService;
import com.cdz360.iot.gw.config.IotConfigProperties;
import com.cdz360.iot.gw.north.model.OrderData;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

/**
 * 订单缓存（在线卡或者云端生成的订单）
 */
@Slf4j
@Service
public class EvseOrderRepository {

    private static final int MAX_DAYS = 1; // 本地缓存保留的天数
    private static final int HB_SEQ_STEP = 4; // 报文序列号步进值

    private static final String CACHE_FILE_NAME = "plugCache.json";

    // 去掉映射关系的订单匹配信息
    private final Map<String, OrderData> orderMap = new ConcurrentHashMap<>();

    // 交易ID 和 订单 映射关系
    private final Map<Integer, String> transMap = new ConcurrentHashMap<>();

    @Autowired
    private RedisOrderCacheService redisOrderCacheService;

    // 本地订单有效时间: 默认3分钟
    @Value("${cdz360.timer.cache-order-timeout:3}")
    private int CACHE_ORDER_TIMEOUT;

    /**
     * 最后一次数据更新时间
     */
    private Long lastUpdateTime;

    /**
     * 最后一次写入到缓存文件的时间
     */
    private Long lastWriteFileTime;

    @Autowired
    private IotConfigProperties configProperties;

    @PostConstruct
    public void init() {
        log.info("开始从缓存文件加载枪头信息");
        File cacheFile = new File(configProperties.getRtCfgPath(), CACHE_FILE_NAME);
        try (FileReader fr = new FileReader(cacheFile); BufferedReader br = new BufferedReader(
            fr)) {
            StringBuilder buf = new StringBuilder();
            while (br.ready()) {
                buf.append(br.readLine());
            }
            if (buf.length() > 0) {
                PlugCacheJson json = JsonUtils.fromJson(buf.toString(), PlugCacheJson.class);
                if (json != null && CollectionUtils.isNotEmpty(json.getOrderNos())) {
                    for (var pair : json.getOrderNos()) {
                        try {
                            transMap.put(pair.getTransId(), pair.getOrderNo());
                        } catch (Exception e) {
                            log.error("构建枪头-订单号缓存信息失败. pair = {}", pair);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("读取枪头信息文件失败. filePath = {}", CACHE_FILE_NAME, e);
        }
        lastUpdateTime = new Date().getTime();
        lastWriteFileTime = lastUpdateTime;
        log.info("从缓存文件加载枪头信息完成");
    }

    @PreDestroy
    public void beforeDestroy() {
        dump2File();
    }

    /**
     * 添加订单信息
     */
    public void addOrder(OrderData order) {
        log.info("add order: {}", order);
        String orderKey = order.getOrderNo();

        order.setUpdateTime(new Date());
        this.orderMap.put(orderKey, order);
        this.transMap.put(order.getTransId(), orderKey);
        lastUpdateTime = new Date().getTime();

        // 订单状态有变更时，将订单更新到redis，缓存有效期7天
        this.redisOrderCacheService.newOrder(order);
    }

    /**
     * 获取订单管理
     */
    public OrderData getOrder(String orderNo) {
        log.trace("get order: orderNo: {}", orderNo);
        // 订单状态有变更时，将订单更新到redis，缓存有效期7天
        // 网关优先取本地缓存, 本地缓存没有或者时间戳超过3分钟(时间可配)，从redis获取订单缓存.
        // 如果redis上的订单的时间戳早于网关本地订单的时间戳，取本地缓存的数据

        OrderData data = this.orderMap.get(orderNo);
        Date now = new Date();
        if (null == data || null == data.getUpdateTime()) {
            Optional<OrderData> order = this.redisOrderCacheService.getOrder(orderNo);
            if (order.isEmpty()) {
                log.info("redis 也不存在该订单: orderNo = {}", orderNo);
                return null;
            } else {
                data = order.get().setUpdateTime(now);
                this.orderMap.put(orderNo, data);
                log.debug("<< get order. orderNo = {}, orderStatus = {}", data.getOrderNo(),
                    data.getOrderStatus());
                return data;
            }
        } else if (data.getUpdateTime()
            .before(new Date(now.getTime() - CACHE_ORDER_TIMEOUT * 60 * 1000))) {
            Optional<OrderData> order = this.redisOrderCacheService.getOrder(orderNo);
            if (order.isEmpty()) {
                log.info("redis 也不存在该订单: orderNo = {}", orderNo);
                return null;
            } else {
                if (data.getUpdateTime().after(order.get().getUpdateTime())) {
                    data.setUpdateTime(now);
                } else {
                    data = order.get().setUpdateTime(now);
                }

                this.redisOrderCacheService.updateOrder(data);
                this.orderMap.put(orderNo, data);
                log.debug("<< get order. orderNo = {}, orderStatus = {}", data.getOrderNo(),
                    data.getOrderStatus());
                return data;
            }
        }
        log.trace("<< get order. orderNo = {}, orderStatus = {}", data.getOrderNo(),
            data.getOrderStatus());
        return data;
    }

    public OrderData getOrder(Integer transId) {
        log.trace("getOrder transId: {}", transId);
        String orderNo = this.transMap.get(transId);
        if (null == orderNo || orderNo.isEmpty()) {
            Optional<String> opt = this.redisOrderCacheService.getOrderNo(transId);
            if (opt.isEmpty()) {
                log.info("redis 也不存在该交易: orderNo = {}", orderNo);
                return null;
            } else {
                orderNo = opt.get();
            }
        }
        return this.getOrder(orderNo);
    }

    /**
     * 充电中订单更新
     *
     * @param traceId
     * @param order   已经从缓存中获取出来的数据
     * @param hbSeq
     * @return
     */
    public void hbUpdateOrder(String traceId, @NonNull OrderData order, Long hbSeq) {
        log.info("充电中更新: traceId = {}, orderNo = {}, lastHbSeq = {}",
            traceId, order.getOrderNo(), order.getLastHbSeq());

        // 心跳序列号比缓存中的序列号 >
        if (null != order.getLastHbSeq() &&
            hbSeq < order.getLastHbSeq() + HB_SEQ_STEP) {
            // 获取redis 中的订单数据
            Optional<OrderData> redisOp = this.redisOrderCacheService.getOrder(order.getOrderNo());
            if (redisOp.isPresent()) {
                // redis 中的订单
                OrderData redisCache = redisOp.get();

                // 本地时间和 redis 中时间比较
                if (!order.getUpdateTime().before(redisCache.getUpdateTime())) {
                    // 使用 redis 中数据
                    this.redisOrderCacheService.updateOrder(order);
                }
                this.orderMap.put(order.getOrderNo(), redisCache);
            } else {
                this.redisOrderCacheService.updateOrder(order);
                this.orderMap.put(order.getOrderNo(), order);
            }
        }
    }

    /**
     * 订单更新or 新增
     *
     * @param order
     */
    public OrderData updateOrder(OrderData order) {
        log.info("update order: {}", order);
        //String orderKey = this.orderKey(order.getEvseId(), (byte) order.getPlugId());
        String orderKeyHex = order.getOrderNo();//24位hex(如果是桩端传过来的则是24位hex;如果是云端传过来的则是12位的 Ascii码）

        //        String orderKeyAscii = new String(ByteUtil.hexStringToByteArray(order.getOrderNo()));//12位 Ascii码 （或6位Ascii码 无效）
        //        if (orderMap.containsKey(orderKeyAscii) || orderMap.containsKey(orderKeyHex)) {
        if (orderMap.containsKey(orderKeyHex)) {
            // 更新信息
            //            OrderData evseOrder = orderMap.containsKey(orderKeyAscii) ? this.orderMap.get(orderKeyAscii) : this.orderMap.get(orderKeyHex);
            OrderData evseOrder = this.orderMap.get(orderKeyHex);

            // evseOrder.setOrderNo(order.getOrderNo());//不更新订单号
            if (order.getOrderStatus() != null
                && !order.getOrderStatus().equals(evseOrder.getOrderStatus())) {
                evseOrder.setOrderStatus(order.getOrderStatus());
            }
            if (order.getBalance() != null) {
                evseOrder.setBalance(order.getBalance());
            }
            if (order.getAmount() != null) {
                evseOrder.setAmount(order.getAmount());
            }
            if (NumberUtils.isZero(evseOrder.getStartTime())
                && NumberUtils.gtZero(order.getStartTime())) {
                evseOrder.setStartTime(order.getStartTime());
            }
            if (order.getStopMode() != null) {
                evseOrder.setStopMode(order.getStopMode());//停止方式
            }
            if (order.getStartType() != null) {
                evseOrder.setStartType(order.getStartType());//启动方式
            }
            if (order.getStartSoc() != null) {
                evseOrder.setStartSoc(order.getStartSoc());
            }
            if (order.getSoc() != null) {
                evseOrder.setSoc(order.getSoc());
            }
            if (order.getPriceCode() != null) {
                evseOrder.setPriceCode(order.getPriceCode());
            }
            if (order.getPrice() != null) {
                evseOrder.setPrice(order.getPrice());
            }
            if (order.getLatestOrderFee() != null) {
                evseOrder.setLatestOrderFee(order.getLatestOrderFee());
            }
            if (order.getRcdBillReqDto() != null) {
                evseOrder.setRcdBillReqDto(order.getRcdBillReqDto());
            }

            if (order.getStartMeter() != null) {
                evseOrder.setStartMeter(order.getStartMeter());
            }
            if (order.getCurrMeter() != null) {
                evseOrder.setCurrMeter(order.getCurrMeter());
            }
            if (order.getLastOrderDetail() != null) {
                evseOrder.setLastOrderDetail(order.getLastOrderDetail());
            }
            if (order.getBillingDetails() != null) {
                evseOrder.setBillingDetails(order.getBillingDetails());
            }

            // 更新 redis
            this.redisOrderCacheService.updateOrder(evseOrder);

            return evseOrder;
        } else {
            log.info("order doesn't exist, create a new order. order: {}.", order);
            this.addOrder(order);

            return order;
        }
    }

    /**
     * 更新订单的待删除标记
     *
     * @param orderNo
     */
    public void updateToNeedDelFlag(String orderNo) {
        if (orderMap.containsKey(orderNo)) {
            // 更新信息
            OrderData evseOrder = this.orderMap.get(orderNo);
            evseOrder.setNeedDel(1);
            evseOrder.setStopFinishTime(System.currentTimeMillis());

//            // 持久化到 sqlite
//            saveOrder(evseOrder.getOrderNo(), DBOperType.SAVE, evseOrder);

            // 收到桩上报充电结束报文时，修改redis缓存的订单同时将有效期改为2小时.
            this.redisOrderCacheService.stopOrder(evseOrder);
        } else {
            log.info("待删除的订单在缓存中不存在。orderNo: {}", orderNo);
        }
    }


    /**
     * 找到1天前就停止的订单然后删除
     */
    public void findAndDelOrder(String tid) {
        long now = System.currentTimeMillis();
        Date limit = new Date(now - MAX_DAYS * 24 * 60 * 60 * 1000);
        int sizeBeforeRemove = this.orderMap.size();
        for (Iterator<Map.Entry<String, OrderData>> it = orderMap.entrySet().iterator();
            it.hasNext(); /* nothing*/) {
            Map.Entry<String, OrderData> item = it.next();
            OrderData order = item.getValue();

            if (order.getUpdateTime() == null) {
                order.setUpdateTime(new Date());
            }

            // 超过指定是时间没有更新的订单下删除
            if (limit.after(order.getUpdateTime())) {
                log.info("[{}] order: {}", tid, order);
                it.remove();
            }
        }
        int sizeAfterRemove = this.orderMap.size();
        log.info("[{}] 删除过期订单完成, 订单数 {} ==> {}", tid, sizeBeforeRemove, sizeAfterRemove);
    }

    /**
     * 将运行态的缓存信息写入到文件
     */
    public synchronized void dump2File() {
        log.info("lastWriteFileTime: {}, lastUpdateTime: {}", lastWriteFileTime, lastUpdateTime);
        if (lastWriteFileTime > lastUpdateTime) {
            return;
        }
        log.info("开始将枪头信息写入到缓存文件");
        PlugCacheJson json = new PlugCacheJson();
        List<PlugNoOrderNoPair> orderNoList = new ArrayList<>();
        if (!transMap.isEmpty()) {
            for (var transId : transMap.keySet()) {
                PlugNoOrderNoPair pair = new PlugNoOrderNoPair(transId, transMap.get(transId));
                orderNoList.add(pair);
            }
        }
        json.setOrderNos(orderNoList);

        File cacheFile = new File(configProperties.getRtCfgPath(), CACHE_FILE_NAME);
        try (FileWriter fr = new FileWriter(cacheFile); BufferedWriter br = new BufferedWriter(
            fr)) {
            fr.write(JsonUtils.toJsonString(json));
            lastWriteFileTime = new Date().getTime();
        } catch (Exception e) {
            log.error("写入枪头信息文件失败. filePath = {}", CACHE_FILE_NAME, e);
        }
        log.info("将枪头信息写入到缓存文件完成");
    }

    @Data
    public static class PlugCacheJson {

        private List<PlugNoOrderNoPair> orderNos;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PlugNoOrderNoPair {

        private Integer transId;
        private String orderNo;
    }

}
