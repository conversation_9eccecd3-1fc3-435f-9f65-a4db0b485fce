package com.cdz360.iot.gw.biz;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.converter.DownloadTypeConverter;
import com.cdz360.iot.gw.model.evse.CloudCmdResultObjectReq;
import com.cdz360.iot.gw.model.evse.CloudEvseUpgradeResult;
import com.cdz360.iot.gw.model.evse.protocol.EvseUpgradeDown;
import com.cdz360.iot.gw.model.evse.protocol.EvseUpgradeDown2;
import com.cdz360.iot.gw.model.gw.CloudUpReq;
import com.cdz360.iot.gw.model.type.CloudResultType;
import com.cdz360.iot.gw.north.mq.model.EvseVersion;
import com.cdz360.iot.gw.north.mq.model.MqEvseUpgradeMsg;
import com.cdz360.iot.gw.north.mq.model.MqMsgBase;
import com.cdz360.iot.gw.north.server.IotClient;
import com.cdz360.iot.gw.south.server.JsonServerImpl;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import eu.chargetime.ocpp.model.firmware.FirmwareStatus;
import eu.chargetime.ocpp.model.firmware.FirmwareStatusNotificationConfirmation;
import eu.chargetime.ocpp.model.firmware.FirmwareStatusNotificationRequest;
import java.util.List;
import java.util.Optional;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class UpgradeProcess {

    private final static String GW_UPGRADE_VERSION = "UPG:VERSION";

    @Autowired
    private IotClient iotClient;

    @Autowired
    private JsonServerImpl jsonServer;

    @Autowired
    private EvseCmdBizService evseCmdBizService;

    @Autowired
    private UpgradeQueueManager upgradeQueueManager;

    @Value("${cdz360.timer.upgrade-report-timeout}")
    private long upgradeReportTimeout;

    public void upgradeEvse(String tid, MqMsgBase<MqEvseUpgradeMsg> request) {
        log.info("[{}] 升级桩固件 request: {}", tid, request);

        try {
            checkRequest(tid, request);
        } catch (Exception e) {
            log.info("[{}] 下发固件升级指令失败 evseNo: {}, error: {}", tid,
                request.getData().getEvseNo(), e.getMessage(), e);
            reportUpgradeResult(tid, request, 99, e.getMessage());
            return;
        }

        try {
            fixRequestData(request); //预处理部分可能非期待的数据

            EvseUpgradeDown upgradeData = makeUpgradeData(tid, request);
            log.info("[{}} 下发升级桩固件指令 evseId: {}", tid, upgradeData.getEvseId());

            this.evseCmdBizService.addCmd(upgradeData.getEvseId(), request);

            boolean success = jsonServer.sendUpdateFirmwareReq(upgradeData);

            if (!success) {
                log.info("[{}] 部分桩下发固件升级指令失败 evseNo: {}", tid,
                    request.getData().getEvseNo());

                //reportResult(tid, request.getData().getTaskNo(), request.getData().getEvseNo(), EvseCfgResultType.FAIL);
                reportUpgradeResult(tid, request, 99, "发送桩软件指令失败");
                return;
            }
        } catch (Exception e) {
            log.error("[{}] 下发固件升级指令时发生错误", tid, e);
            reportUpgradeResult(tid, request, 99, "发送桩软件指令失败");
            return;
        }

        Timer timer = new Timer("Timer-Upgrade");
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                log.info("[{}} 超时上报固件升级结果", tid);

                try {
                    MqMsgBase msg = evseCmdBizService.removeCmd(request.getData().getEvseNo());
                    if (msg != null) {
                        reportUpgradeResult(tid, request, 98, "桩响应超时");
                    }
                } catch (Exception e) {
                    log.error("[{}] 超时上报固件升级结果失败", tid, e);
                }
                log.info("[{}] 固件升级结果已经全部上报超时完成", tid);
                timer.cancel();
            }
        }, upgradeReportTimeout);
    }

    public void upgradeEvse2(String tid, MqMsgBase<MqEvseUpgradeMsg> request) {
        String evseNo = request.getData().getEvseNo();
        log.info("[{} {}] 升级桩固件 request: {}", tid, evseNo, request);

        try {
            checkRequest2(tid, request);
        } catch (Exception e) {
            log.info("[{}] 下发固件升级指令失败 evseNo: {}, error: {}", tid, evseNo, e.getMessage(),
                e);
            reportUpgradeResult(tid, request, 99, e.getMessage());
            return;
        }

        try {
            EvseUpgradeDown2 upgradeData = makeUpgradeData2(tid, request);
            log.info("[{}} 下发升级桩固件指令 evseId: {}", tid, upgradeData.getEvseId());

            List<EvseVersion> versionList = upgradeData.getEvseVersionList();
            if (versionList == null || versionList.isEmpty()) {
                log.warn("[{} {}] 升级文件列表为空", tid, evseNo);
                throw new DcServiceException("升级文件列表为空");
            }
            // 只发送第一个文件的升级请求，其他文件放入队列等待
            EvseVersion firstVersion = versionList.get(0);
            String accountNo = upgradeData.getAccountNo();
            String password = upgradeData.getPassword();
            // 将升级队列存储到缓存中
            if (versionList.size() > 1) {
                List<EvseVersion> remainingVersions = versionList.subList(1, versionList.size());
                upgradeQueueManager.addUpgradeQueue(tid, evseNo, remainingVersions, accountNo,
                    password);
            }

            this.evseCmdBizService.addCmd(upgradeData.getEvseId(), request);

            boolean success = jsonServer.sendUpdateFirmwareReq2(upgradeData.getBase(), firstVersion,
                accountNo, password);

            if (!success) {
                log.info("[{}] 部分桩下发固件升级指令失败 evseNo: {}", tid, evseNo);

                reportUpgradeResult(tid, request, 99, "发送桩软件指令失败");
                return;
            }
        } catch (Exception e) {
            log.error("[{} {}] 下发固件升级指令时发生错误: {}", tid, evseNo, e.getMessage(), e);
            reportUpgradeResult(tid, request, 99, "发送桩软件指令失败");
            return;
        }

        Timer timer = new Timer("Timer-Upgrade");
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                log.info("[{} {}} 超时上报固件升级结果", tid, evseNo);

                try {
                    MqMsgBase msg = evseCmdBizService.removeCmd(evseNo);
                    if (msg != null) {
                        reportUpgradeResult(tid, request, 98, "桩响应超时");
                    }
                } catch (Exception e) {
                    log.error("[{} {}] 超时上报固件升级结果失败", tid, evseNo, e);
                }
                log.info("[{} {}] 固件升级结果已经全部上报超时完成", tid, evseNo);
                timer.cancel();
            }
        }, upgradeReportTimeout);
    }

    private boolean checkRequest(String tid, MqMsgBase<MqEvseUpgradeMsg> request) {
        // try {

        if (request == null || request.getData() == null) {
            log.warn("[{}] 平台下发请求错误 request is null", tid);
            //return false;
            throw new DcServiceException("平台下发请求错误");
        }
        MqEvseUpgradeMsg msg = request.getData();
        if (StringUtils.isEmpty(msg.getEvseNo())) {
            log.info("[{}] 升级桩固件时未指定桩号", tid);
            //return false;
            throw new DcServiceException("升级桩固件时未指定桩号");
        }

        if (!StringUtils.isEmpty(msg.getDownloadUsername())
            && msg.getDownloadUsername().length() > 8) {
            log.info("[{}] 下载帐号名太长 userName: {}", tid, msg.getDownloadUsername());
            //return false;
            throw new DcServiceException("下载帐号名太长");
        }

        if (!StringUtils.isEmpty(msg.getDownloadPasscode())
            && msg.getDownloadPasscode().length() > 8) {
            log.info("[{}} 下载帐号的密码太长 password: {}", tid, msg.getDownloadPasscode());
            //return false;
            throw new DcServiceException("下载帐号的密码太长");
        }

        if (StringUtils.isEmpty(msg.getDownloadType())) {
            log.info("[{}] 下载类型未指定 downloadType: {}", tid, msg.getDownloadType());
            //return false;
            throw new DcServiceException("下载类型未指定");
        }

        if (msg.getEvseBundle() != null && msg.getEvseBundle().size() > 0) {
            long cnt = msg.getEvseBundle().stream().filter(
                    x -> !StringUtils.isEmpty(x.getUrl()) && x.getUrl().getBytes().length > 0xff)
                .count();
            if (cnt > 0) {
                log.info("[{}] 下载地址过长 versions: {}", tid, msg.getEvseBundle());
                //return false;
                throw new DcServiceException("下载地址过长");
            }
        }

        String taskNo = msg.getTaskNo();//版本号
        //String cacheVersion = idempotenceService.get(GW_UPGRADE_VERSION);

        if (StringUtils.isEmpty(taskNo)) {
            log.info("[{}] 云端下发固件升级的版本号非法", tid);
            //return false;
            throw new DcServiceException("云端下发固件升级的版本号非法");
        }

        return true;
    }

    private boolean checkRequest2(String tid, MqMsgBase<MqEvseUpgradeMsg> request) {

        if (request == null || request.getData() == null) {
            log.warn("[{}] 平台下发请求错误 request is null", tid);
            throw new DcServiceException("平台下发请求错误");
        }
        MqEvseUpgradeMsg msg = request.getData();
        if (StringUtils.isEmpty(msg.getEvseNo())) {
            log.info("[{}] 升级桩固件时未指定桩号", tid);
            throw new DcServiceException("升级桩固件时未指定桩号");
        }

        if (StringUtils.isNotEmpty(msg.getDownloadUsername())
            && msg.getDownloadUsername().length() > 255) {
            log.info("[{}] 下载帐号名太长 userName: {}", tid, msg.getDownloadUsername());
            throw new DcServiceException("下载帐号名太长");
        }

        if (StringUtils.isNotEmpty(msg.getDownloadPasscode())
            && msg.getDownloadPasscode().length() > 255) {
            log.info("[{}} 下载帐号的密码太长 password: {}", tid, msg.getDownloadPasscode());
            throw new DcServiceException("下载帐号的密码太长");
        }

        if (CollectionUtils.isNotEmpty(msg.getFileVoList())) {
            long cnt = msg.getFileVoList().stream().filter(x -> StringUtils.isNotEmpty(x.getUrl()))
                .count();
            if (cnt != msg.getFileVoList().size()) {
                log.info("[{}] 存在无效url. cnt: {}", tid, cnt);
                throw new DcServiceException("存在无效url");
            }
        }

        String taskNo = msg.getTaskNo();//版本号
        //String cacheVersion = idempotenceService.get(GW_UPGRADE_VERSION);

        if (StringUtils.isEmpty(taskNo)) {
            log.info("[{}] 云端下发固件升级的版本号非法", tid);
            //return false;
            throw new DcServiceException("云端下发固件升级的版本号非法");
        }

        return true;
    }

    private void fixRequestData(MqMsgBase<MqEvseUpgradeMsg> request) {
        MqEvseUpgradeMsg msg = request.getData();
        if (StringUtils.isEmpty(msg.getDownloadUsername())) {
            msg.setDownloadUsername("");
        }

        if (StringUtils.isEmpty(msg.getDownloadPasscode())) {
            msg.setDownloadPasscode("");
        }

        if (msg.getEvseBundle() != null && msg.getEvseBundle().size() > 0) {
            msg.getEvseBundle().stream().forEach(x -> {

                if (StringUtils.isEmpty(x.getName())) {
                    x.setName("");
                }

                if (StringUtils.isEmpty(x.getUrl())) {
                    x.setUrl("");
                }

                if (x.getVendorCode() == null) {
                    x.setVendorCode((short) 0);
                }

                if (x.getSwVer() == null) {
                    x.setSwVer((short) 0);
                }
            });
        }
    }

    private EvseUpgradeDown makeUpgradeData(String tid, MqMsgBase<MqEvseUpgradeMsg> request) {
        MqEvseUpgradeMsg mqData = request.getData();
        EvseMsgBase base = new EvseMsgBase();
        base.setTid(tid);
        base.setEvseNo(mqData.getEvseNo());
        EvseUpgradeDown upgradeData = new EvseUpgradeDown(base);

        upgradeData.setSeqNo(SeqGeneratorUtil.newIntegerId(mqData.getEvseNo()));
        upgradeData.setEvseId(mqData.getEvseNo());
        upgradeData.setDownloadType(DownloadTypeConverter.convert(mqData.getDownloadType()));
        upgradeData.setAccountNo(mqData.getDownloadUsername());
        upgradeData.setPassword(mqData.getDownloadPasscode());

        Optional<EvseVersion> pc01Version = mqData.getEvseBundle().stream().filter(
                x -> !StringUtils.isEmpty(x.getName()) && "PC01".equals(x.getName().toUpperCase()))
            .findFirst();
        Optional<EvseVersion> pc02Version = mqData.getEvseBundle().stream().filter(
                x -> !StringUtils.isEmpty(x.getName()) && "PC02".equals(x.getName().toUpperCase()))
            .findFirst();
        Optional<EvseVersion> pc03Version = mqData.getEvseBundle().stream().filter(
                x -> !StringUtils.isEmpty(x.getName()) && "PC03".equals(x.getName().toUpperCase()))
            .findFirst();

        if (pc01Version.isPresent()) {
            EvseVersion ver = pc01Version.get();

            upgradeData.setPc01VendorCode(ver.getVendorCode());
            upgradeData.setPc01SwVer(ver.getSwVer());
            upgradeData.setPc01Url(ver.getUrl());
        }

        if (pc02Version.isPresent()) {
            EvseVersion ver = pc02Version.get();

            upgradeData.setPc02VendorCode(ver.getVendorCode());
            upgradeData.setPc02SwVer(ver.getSwVer());
            upgradeData.setPc02Url(ver.getUrl());
        }

        if (pc03Version.isPresent()) {
            EvseVersion ver = pc03Version.get();

            upgradeData.setPc03VendorCode(ver.getVendorCode());
            upgradeData.setPc03SwVer(ver.getSwVer());
            upgradeData.setPc03Url(ver.getUrl());
        }
        return upgradeData;
    }

    private EvseUpgradeDown2 makeUpgradeData2(String tid, MqMsgBase<MqEvseUpgradeMsg> request) {
        MqEvseUpgradeMsg mqData = request.getData();
        EvseMsgBase base = new EvseMsgBase();
        base.setTid(tid);
        base.setEvseNo(mqData.getEvseNo());
        EvseUpgradeDown2 upgradeData = new EvseUpgradeDown2(base);

        upgradeData.setSeqNo(SeqGeneratorUtil.newIntegerId(mqData.getEvseNo()));
        upgradeData.setEvseId(mqData.getEvseNo());
        upgradeData.setAccountNo(mqData.getDownloadUsername());
        upgradeData.setPassword(mqData.getDownloadPasscode());

        upgradeData.setEvseVersionList(mqData.getFileVoList().stream().map(e -> {
            EvseVersion ev = new EvseVersion();
            ev.setName(e.getName());
            ev.setUrl(e.getUrl());
            return ev;
        }).collect(Collectors.toList()));
        return upgradeData;
    }

    public void reportUpgradeResult(String traceId, MqMsgBase dwMsgBase, int result,
        String errorMsg) {
        MqEvseUpgradeMsg dwMsg = (MqEvseUpgradeMsg) dwMsgBase.getData();
        CloudUpReq<CloudCmdResultObjectReq<CloudEvseUpgradeResult>> upMsg = new CloudUpReq<>();

        CloudCmdResultObjectReq<CloudEvseUpgradeResult> data = new CloudCmdResultObjectReq<>();

        CloudEvseUpgradeResult detail = new CloudEvseUpgradeResult();
        detail.setEvseNo(dwMsg.getEvseNo()).setTaskNo(dwMsg.getTaskNo());
        if (result == IotGwConstants.SUCCESS) {
            detail.setResult(CloudResultType.SUCCESS);
        } else if (result == 0x01) {
            detail.setResult(CloudResultType.FAIL).setMsg("桩充电中,不允许升级");
        } else if (result == 0x02) {
            detail.setResult(CloudResultType.FAIL).setMsg("故障状态不允许升级");
        } else if (result == 0x03) {
            detail.setResult(CloudResultType.FAIL).setMsg("下载升级包失败");
        } else if (result == 0x04) {
            detail.setResult(CloudResultType.FAIL).setMsg("存储升级包失败");
        } else if (result == 0x05) {
            detail.setResult(CloudResultType.FAIL).setMsg("升级异常中止");
        } else if (result == 0x06) {
            detail.setResult(CloudResultType.FAIL).setMsg("DTU类型不支持");
        } else if (result == 98) {
            detail.setResult(CloudResultType.TIMEOUT).setMsg("桩响应超时");
        } else if (!StringUtils.isEmpty(errorMsg)) {
            detail.setResult(CloudResultType.FAIL).setMsg(errorMsg);
        } else {
            detail.setResult(CloudResultType.FAIL).setMsg("桩返回升级失败. code = " + result);
        }

        data.setCmd(dwMsgBase.getCmd());
        data.setSeq(dwMsgBase.getSeq());
        data.setStatus(result);

        data.setDetail(detail);
        upMsg.setSeq(SeqGeneratorUtil.newStringId());
        upMsg.setData(data);
        iotClient.sendCmdResult(traceId, upMsg);
    }

    public void evseReup(String traceId, GwEvseVo evse) {
        boolean existKey = evseCmdBizService.existKey(evse.getEvseNo());
        log.info("[{}] evseReup. existKey: {}", traceId, existKey);
        if (existKey) {
            MqMsgBase msg = evseCmdBizService.removeCmd(evse.getEvseNo());
            reportUpgradeResult(traceId, msg, IotGwConstants.SUCCESS, null);
        }
    }

    public Confirmation reportUpgradeResultByFirmwareStatus(
        FirmwareStatusNotificationRequest request) {
        String tid = request.getBase().getTid();
        String evseNo = request.getBase().getEvseNo();
        boolean existKey = evseCmdBizService.existKey(evseNo);
        log.info("[{}] reportUpgradeResult. existKey: {}", tid, existKey);
        if (existKey) {
            Optional<Boolean> succeedOpt = Optional.empty();
            AtomicInteger resultAto = new AtomicInteger(0);

            FirmwareStatus status = request.getStatus();
            switch (status) {
                case DownloadFailed:
                    succeedOpt = Optional.of(false);
                    resultAto.set(0x03);
                    break;
                case InstallationFailed:
                    succeedOpt = Optional.of(false);
                    resultAto.set(0x05);
                    break;
                case Idle:
                case Downloading:
                case Downloaded:
                case Installing:
                    log.info("[{}] 桩固件状态: {}，不需要上报", tid, status);
                    break;
                case Installed:
                    // 当前文件安装成功，尝试处理下一个升级文件
                    succeedOpt = Optional.of(true);
                    resultAto.set(IotGwConstants.SUCCESS);
                    try {
                        Optional<Boolean> booleanOpt = jsonServer.processNextUpgrade(evseNo);
                        if (booleanOpt.isPresent()) {
                            if (booleanOpt.get()) {
                                // 如果还有下一个文件要升级，不上报最终结果，等待下一个文件完成
                                log.info("[{}] 桩 {} 开始处理下一个升级文件，暂不上报结果", tid,
                                    evseNo);
                                succeedOpt = Optional.empty();
                            } else {
                                succeedOpt = Optional.of(false);
                                resultAto.set(0x05);
                            }
                        } else {
                            log.info("[{} {}] 所有升级文件处理完成", tid, evseNo);
                        }
                    } catch (Exception e) {
                        succeedOpt = Optional.of(false);
                        resultAto.set(0x05);
                        log.error("[{} {}] 桩处理下一个升级文件时发生错误: {}", tid, evseNo,
                            e.getMessage(), e);
                    }
                    break;
                default:
                    log.error("[{}] 未支持的桩固件状态: {}", tid, status);
                    break;
            }

            succeedOpt.ifPresent(x -> {
                upgradeQueueManager.clearUpgradeQueue(evseNo);
                log.info("[{} {}] 升级任务处理结束，清理桩升级队列", tid, evseNo);

                MqMsgBase msg = evseCmdBizService.removeCmd(evseNo);
                reportUpgradeResult(tid, msg, resultAto.get(), null);
            });
        }
        return new FirmwareStatusNotificationConfirmation();
    }

}
