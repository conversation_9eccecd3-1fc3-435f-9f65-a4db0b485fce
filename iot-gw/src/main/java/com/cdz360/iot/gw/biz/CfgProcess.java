package com.cdz360.iot.gw.biz;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.charge.vo.ChargePriceItem;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.biz.ds.CardConfigCache;
import com.cdz360.iot.gw.biz.ds.ConfigCache;
import com.cdz360.iot.gw.biz.ds.IdempotenceService;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.evse.CloudConfig;
import com.cdz360.iot.gw.model.evse.protocol.CardModifyDown;
import com.cdz360.iot.gw.model.type.ConstantCollections;
import com.cdz360.iot.gw.model.type.EvseCfgResultType;
import com.cdz360.iot.gw.north.model.ConfigModifyResult;
import com.cdz360.iot.gw.north.model.EvseCfgGetResponse;
import com.cdz360.iot.gw.north.model.GetConfig;
import com.cdz360.iot.gw.north.model.GetConfigResponse;
import com.cdz360.iot.gw.north.model.SubCfgResult;
import com.cdz360.iot.gw.north.mq.model.ConfigModifyRequest;
import com.cdz360.iot.gw.north.server.ConfigService;
import com.cdz360.iot.gw.south.server.JsonServerImpl;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Timer;
import java.util.TimerTask;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 配置下发处理
 */
@Service
@Scope(value = "singleton")
public class CfgProcess {

    private final Logger logger = LoggerFactory.getLogger(CfgProcess.class);

    @Autowired
    private ConfigService configService;

    @Autowired
    private IdempotenceService idempotenceService;

    @Autowired
    private ConfigCache configCache;

    @Autowired
    private CacheManagerService cacheManagerService;

    @Autowired
    private JsonServerImpl jsonServer;

    @Autowired
    private CardConfigCache cardConfigCache;

    @Autowired
    private CardProcess cardProcess;

    /*
    @Autowired
    private VinProcess vinProcess;
    */

    @Value("${cdz360.timer.cfg-report-timeout}")
    private long cfgReportTimeout;

    /**
     * 处理收到的桩配置更新指令后操作 操作云端桩配置更新 请求相关云端变更的配置、下发到桩端
     *
     * @param request 云端下发的指令
     */
    public void processEvseCfg(String traceId, ConfigModifyRequest request) {

        logger.info("[{}] 下发桩配置 request: {}", traceId, request);

        String cfgVer = request.getData().getCfgVer();//版本号
        String cacheVersion = idempotenceService.get(ConstantCollections.GW_CFG_VERSION);

        validateRequest(traceId, request, cfgVer, cacheVersion);

        idempotenceService.put(ConstantCollections.GW_CFG_VERSION, cfgVer);

        try {

            request.getData().getEvseNos().forEach(
                evseId -> configCache.put(ConstantCollections.GW_CFG_VERSION, evseId, cfgVer));

            List<String> failEvses = new ArrayList<>();//更新失败的桩号

            //TODO:排重
            List<String> evseIds = request.getData().getEvseNos().stream()
                .filter(evseId -> !StringUtils.isEmpty(evseId)).collect(Collectors.toList());

            int i = 1;
            for (String evseId : evseIds) {

                try {
                    GetConfig config = new GetConfig();
                    config.setCfgVer(cfgVer);
                    config.setEvseNo(evseId);

                    EvseCfgGetResponse response = configService.evseCfgGet(traceId, config);

                    if (ObjectUtils.isEmpty(response) || ObjectUtils.isEmpty(response.getData())) {
                        logger.info("[{}] 下发配置时未获取到云端最新配置。request: {}", traceId,
                            request);
                        failEvses.add(evseId);
                        i++;
                        continue;
                    }
                    GetConfigResponse cfg = response.getData();
                    logger.info("[{}] evseCfgGet.cfg = {}", traceId, cfg);
                    EvseMsgBase base = new EvseMsgBase();
                    base.setTid(traceId);
                    base.setEvseNo(evseId);

                    boolean success = true;

                    if (response.getData().getWhiteCards() != null && !response.getData()
                        .getWhiteCards().isEmpty()) {
                        // TODO: 2025/5/26 WZFIX 后续需删除紧急卡下发逻辑
                        logger.info("[{}] 需要下发紧急卡 card size: {}", traceId,
                            response.getData().getWhiteCards().size());

                        CloudConfig cloudConfig = new CloudConfig();
                        cloudConfig.setCfgVer(cfgVer);
                        cardConfigCache.put(ConstantCollections.GW_CFG_CARD, evseId,
                            JsonUtils.toJsonString(cloudConfig));
                        // 已在里面做失败上报操作
                        CardModifyDown dMsg = new CardModifyDown(base);
                        dMsg.setWhiteCards(cfg.getWhiteCards());
                        cardProcess.deliveryCard(dMsg, cfgVer);

                    } else if (CollectionUtils.isNotEmpty(response.getData().getLocalCards())) {
                        logger.info("[{}] 需要下发本地卡 card size: {}", traceId,
                            response.getData().getLocalCards().size());

                        CloudConfig cloudConfig = new CloudConfig();
                        cloudConfig.setCfgVer(cfgVer);
                        cardConfigCache.put(ConstantCollections.GW_CFG_CARD, evseId,
                            JsonUtils.toJsonString(cloudConfig));
                        // 已在里面做失败上报操作
                        CardModifyDown dMsg = new CardModifyDown(base);
                        dMsg.setWhiteCards(cfg.getLocalCards());
                        cardProcess.deliveryCard(dMsg, cfgVer);

                    } else if (response.getData().getWhiteVinList() != null) {
                        logger.info("[{}] 需要下VIN本地鉴权 vin size: {}", traceId,
                            response.getData().getWhiteVinList().size());
                        /*
                        CloudConfig cloudConfig = new CloudConfig();
                        cloudConfig.setCfgVer(cfgVer);
                        cardConfigCache.put(
                            ConstantCollections.GW_CFG_VIN, evseId,
                            JsonUtils.toJsonString(cloudConfig));
                        // 已在里面做失败上报操作
                        VinModifyDown dMsg = new VinModifyDown(base);
                        List<WhiteVin> whiteVins = response.getData().getWhiteVinList();
                        dMsg.setWhiteVins(whiteVins);
                        deliveryVinConfig(traceId, dMsg, cfgVer);
                        */
                        success = false;

                    } else {

                        SubCfgResult subCfgResult = new SubCfgResult();

                        if (cfg.getPriceCode() != null && CollectionUtils.isNotEmpty(
                            cfg.getPrice())) {
                            logger.info(
                                "[{} {}] 需要缓存或下发桩计费信息 priceCode: {}, priceSize: {}",
                                traceId, evseId, cfg.getPriceCode(), cfg.getPrice().size());

                            ChargePriceVo price = new ChargePriceVo();
                            price.setId(Long.valueOf(cfg.getPriceCode()));
                            price.setItemList(new ArrayList<>());
                            for (var pi : cfg.getPrice()) {
                                ChargePriceItem cpi = new ChargePriceItem();
                                cpi.setCode(pi.getCode()).setStartTime(pi.getStartTime())
                                    .setEndTime(pi.getStopTime()).setElecPrice(pi.getElecPrice())
                                    .setServPrice(pi.getServPrice());
                                price.getItemList().add(cpi);
                            }

                            Optional<GwEvseVo> evse = cacheManagerService.getEvseCache(evseId);
                            if (evse.isPresent()) {
                                GwEvseVo gwEvseVo = evse.get();

                                // 润诚达的桩要下发计费指令
                                Optional<Boolean> resultOpt = jsonServer.sendRcdEvsePriceByConditions(
                                    base, price);

                                boolean priceSendRes = true; // 默认成功
                                boolean priceCacheOrNot = true; // 默认缓存
                                if (resultOpt.isPresent()) {
                                    priceSendRes = resultOpt.get();
                                    priceCacheOrNot = resultOpt.get();
                                }

                                // 网关本地先缓存住计费信息
                                if (priceCacheOrNot) {
                                    gwEvseVo.setPriceCode(Long.valueOf(cfg.getPriceCode()));
                                    gwEvseVo.setPrice(price);
                                    cacheManagerService.updateAndSync2OtherCache(gwEvseVo);
                                }

                                subCfgResult.setChargeResult(
                                    priceSendRes ? DcConstants.KEY_RES_CODE_SUCCESS
                                        : DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
                                success = success && priceSendRes;
                            } else {
                                success = false;
                            }
                        }

                        if (StringUtils.isNotBlank(cfg.getQrUrl())) {
                            logger.info("[{} {}] 需要下发二维码 qrUrl: {}", traceId, evseId,
                                cfg.getQrUrl());

                            Optional<Boolean> opt = jsonServer.sendQrCodeByConditions(base,
                                cfg.getQrUrl());
                            if (opt.isPresent()) {
                                boolean qrcodeSendRes = opt.get();
                                subCfgResult.setQrResult(
                                    qrcodeSendRes ? DcConstants.KEY_RES_CODE_SUCCESS
                                        : DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
                                success = success && qrcodeSendRes;
                            } else {
                                success = false;
                            }
                        }

                        if (subCfgResult.getChargeResult() != null
                            || subCfgResult.getQrResult() != null) {
                            // 直接上报结果
                            EvseCfgResultType resultType =
                                success ? EvseCfgResultType.SUCCESS : EvseCfgResultType.FAIL;
                            this.reportByEvse(traceId, evseId, resultType, subCfgResult);
                            continue; // 已经上报，不用加到failEvses，跳过
                        } else {
                            logger.error("[{} {}] 暂未支持的配置下发类型 ", traceId, evseId);
                            success = false;
                        }

                    }

                    if (!success) {
                        failEvses.add(evseId);
                    }

                    i++;
                } catch (Exception ex) {
                    failEvses.add(evseId);
                    logger.error("[{}] 下发配置/紧急卡到桩时发生错误 error: {}", traceId,
                        ex.getMessage(), ex);
                }
            }

            //对于没有拉到配置信息的桩或者桩已经离线的，回复云端更新结果(失败）
            if (!CollectionUtils.isEmpty(failEvses)) {

                logger.info("[{}] 部分桩下发配置失败 evseIds: {}", traceId, failEvses);

                for (String evseId : failEvses) {
                    reportCfgResult(traceId, cfgVer, evseId, EvseCfgResultType.FAIL, null);
                }
            }

        } catch (Exception e) {
            logger.error("[{}] 下发桩配置时发生错误 error: {}", traceId, e.getMessage(), e);
        }

        if (configCache.size() <= 0) {
            logger.info("[{}] 桩配置结果已经全部上报完成 ", traceId);
            idempotenceService.remove(ConstantCollections.GW_CFG_VERSION);
            return;
        }

        Timer timer = new Timer("Timer-Config");
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                logger.info("[{}] 超时上报桩配置结果 ", traceId);

                try {

                    for (String evseId : request.getData().getEvseNos()) {
                        if (configCache.containsKey(ConstantCollections.GW_CFG_VERSION, evseId)) {
                            reportCfgResult(traceId, cfgVer, evseId, EvseCfgResultType.TIMEOUT,
                                null);
                        }
                    }

                } catch (Exception e) {
                    logger.error("[{}] 超时上报桩配置结果失败 error: {}", traceId, e.getMessage(),
                        e);
                }

                logger.info("[{}] 桩配置结果已经全部上报超时完成 ", traceId);
                idempotenceService.remove(ConstantCollections.GW_CFG_VERSION);

                timer.cancel();
            }
        }, cfgReportTimeout);
    }

    private void validateRequest(String traceId, ConfigModifyRequest request, String cfgVer,
        String cacheVersion) {
        if (StringUtils.isEmpty(cfgVer)) {
            logger.info("[{}] 云端下发配置的版本号非法 ", traceId);
            throw new IllegalArgumentException("云端下发配置的版本号非法");
        }

        if (CollectionUtils.isEmpty(request.getData().getEvseNos())) {
            logger.info("[{}] 没有需要下发配置的桩 ", traceId);
            throw new IllegalArgumentException("没有需要下发配置的桩");
        }

//        if (!StringUtils.isEmpty(cacheVersion)) {
//            logger.info("网关正在处理桩配置更新操作，收到重复操作指令。traceId: {}", traceId);
//
//            reportFailedResult(traceId, request);
//
//            throw new IllegalArgumentException("收到重复操作指令");
//        }

        if (cfgVer.equals(cacheVersion)) {
            logger.info("[{}] 收到重复版本的桩配置下发指令。 cfgVersion: {}, request: {}", traceId,
                cfgVer, request);

            reportFailedResult(traceId, request);

            throw new IllegalArgumentException("收到重复版本的桩配置下发指令");
        }
    }

    private void reportFailedResult(String traceId, ConfigModifyRequest request) {
        logger.info("[{}] 上报配置结果为失败 ", traceId);
        if (request != null && !StringUtils.isEmpty(request.getData().getCfgVer())
            && !CollectionUtils.isEmpty(request.getData().getEvseNos())) {
            request.getData().getEvseNos().forEach(evseId -> {
                reportCfgResult(traceId, request.getData().getCfgVer(), evseId,
                    EvseCfgResultType.FAIL, null);
            });
        }
    }

    /*
    private boolean deliveryVinConfig(String traceId, VinModifyDown cloudConfig, String cfgVer) {
        try {
            logger.info("下发VIN到桩。traceId: {}, cloudConfig; {}", traceId, cloudConfig);

            if (cloudConfig == null) {
                throw new IllegalArgumentException("未获取到平台的配置");
            }

            boolean success = vinProcess.deliveryVin(traceId, cloudConfig);

            if (!success) {
                vinProcess.reportVinResult(traceId, cloudConfig.getBase().getEvseNo(),
                    cfgVer, EvseCfgResultType.FAIL, ConstantCollections.ERROR);//本地VIN
            }

            return success;
        } catch (Exception ex) {
            logger.error("下发VIN时发生错误。traceId: {}, request: {}", traceId,
                cloudConfig.getBase().getEvseNo(), ex);
        }

        return false;
    }
    */

    /*
    private boolean deliveryConfig(String tid, CfgModifyDown cloudConfig) {

        try {

            logger.info("[{}] 下发配置到桩。cloudConfig; {}",
                tid, cloudConfig);

            if (cloudConfig == null) {
                throw new IllegalArgumentException("未获取到平台的配置");
            }

            String evseNo = cloudConfig.getBase().getEvseNo();

            String longCipher = cloudConfig.getEvsePasscode();//长效密钥

            return configModifyCommander.sendReply(tid, cloudConfig);

        } catch (Exception ex) {
            logger.error("[{} {}] 下发桩配置时发生错误。", tid, cloudConfig.getBase().getEvseNo(),
                ex);

            return false;
        }
    }
    */

    public void reportByEvse(String tid, String evseNo, EvseCfgResultType resultType,
        SubCfgResult subResult) {
        logger.info("[{} {}] 桩自行上报配置结果 resultType: {}, subResult: {}", tid, evseNo,
            resultType, subResult);

        String cfgVer = configCache.get(ConstantCollections.GW_CFG_VERSION, evseNo);

        //cfgVer == null说明超时器已经上报过了
        if (cfgVer == null) {
            logger.info("[{} {}] 桩自行上报配置结果失败，定时器已经完成上报。", tid, evseNo);
            return;
        }

        reportCfgResult(tid, cfgVer, evseNo, resultType, subResult);

        //如果是最后一台桩上报(或者只有一台桩)，立即删除幂等性缓存，不必等待超时器来清除幂等性缓存，方便在下发完成后可以立即下发。
        if (configCache.size() <= 0) {
            logger.info("[{} {}] 桩配置结果已经全部上报完成。", tid, evseNo);
            idempotenceService.remove(ConstantCollections.GW_CFG_VERSION);
        }
    }

    /**
     * 回复云端更新结果(成功/失败/超时）
     */
    private void reportCfgResult(String traceId, String version, String evseId,
        EvseCfgResultType resultType, SubCfgResult subResult) {

        try {
            logger.info("[{} {}] 上报桩配置结果 version: {}, resultType: {}, subResult: {}",
                traceId, evseId, version, resultType, subResult);

            ConfigModifyResult request = new ConfigModifyResult();
            request.setCfgVer(version);
            request.setEvseNo(evseId);
            // request.setMethod(RequestMethod.EVSE_CFG_RESULT);
            // request.setType(IotPackageType.REQ);
            // request.setSeq(SeqGeneratorUtil.newStringId());
            request.setResult(resultType.name());

            if (!ObjectUtils.isEmpty(subResult)) {
                request.setAdminCodeResult(subResult.getAdminCodeResult());
                request.setTriggerResult(subResult.getTriggerResult());
                // request.setWhiteCardsResult(subResult.getWhiteCardsResult());
                request.setChargeResult(subResult.getChargeResult());
                request.setQrResult(subResult.getQrResult());
            }

            configService.reportModifyResult(traceId, request);
            configCache.remove(ConstantCollections.GW_CFG_VERSION, evseId);

        } catch (Exception e) {
            logger.error("[{} {}] 上报桩配置结果时发生错误。", traceId, evseId, e);
        }
    }
/*
    public void getConfig(String traceId, ConfigQueryRequest request) {
        logger.info("[{}] 云端下发桩配置指令。", traceId);

        if (request == null) {
            logger.error("[{}] 平台下发获取桩配置的指令错误。 request is null", traceId);
            throw new IllegalArgumentException("平台下发获取桩配置的指令错误");
        }

        if (StringUtils.isEmpty(request.getData())) {
            logger.error("[{}] 云端获取桩配置时未指定桩号。", traceId);
            throw new IllegalArgumentException("云端获取桩配置时未指定桩号");
        }

//        ConfigQueryData configQueryData = new ConfigQueryData();
//        configQueryData.setEvseId(request.getData());
//        configQueryData.setSeqNo(request.getSeq());
        EvseMsgBase base = new EvseMsgBase();
        base.setEvseNo(request.getData());
        base.setTid(traceId);
        CfgReadDown dMsg = new CfgReadDown(base);
        boolean result = configQueryCommander.sendReply(request.getSeq(), dMsg);

        if (result) {
            logger.info("[{}] 下发云端获取桩配置指令完成。 seqNo: {}, evseId: {}", traceId,
                request.getSeq(), request.getData());
        } else {
            logger.warn("[{}] 下发云端获取桩配置指令失败。 seqNo: {}, evseId: {}", traceId,
                request.getSeq(), request.getData());
        }
    }
    */
}
