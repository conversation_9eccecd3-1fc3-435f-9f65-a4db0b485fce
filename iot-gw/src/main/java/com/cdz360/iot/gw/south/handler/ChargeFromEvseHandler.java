package com.cdz360.iot.gw.south.handler;

import static com.cdz360.iot.gw.model.type.ChargeMode.FULL;

import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.iot.gw.biz.ds.EvseOrderRepository;
import com.cdz360.iot.gw.biz.ds.PlugCache;
import com.cdz360.iot.gw.config.IotConfigProperties;
import com.cdz360.iot.gw.core.biz.OrderBizService;
import com.cdz360.iot.gw.model.converter.AuthCodeConverter;
import com.cdz360.iot.gw.model.evse.protocol.Charge4EvseReq;
import com.cdz360.iot.gw.model.evse.protocol.Charge4EvseRes;
import com.cdz360.iot.gw.model.evse.protocol.EvseTranx;
import com.cdz360.iot.gw.model.type.ChargeMode;
import com.cdz360.iot.gw.model.type.ConstantCollections;
import com.cdz360.iot.gw.model.type.OrderStopMode;
import com.cdz360.iot.gw.north.model.OrderCreateData;
import com.cdz360.iot.gw.north.model.OrderCreateInfo;
import com.cdz360.iot.gw.north.model.OrderCreateResponse;
import com.cdz360.iot.gw.north.model.OrderData;
import com.cdz360.iot.gw.util.DecimalUtils;
import com.cdz360.iot.gw.util.StrUtil;
import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.model.core.IdTagInfo;
import eu.chargetime.ocpp.model.core.StartTransactionConfirmation;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * 桩端发起充电请求
 */
@Slf4j
@Component
public class ChargeFromEvseHandler extends UpstreamHandler2 {


    @Autowired
    private EvseOrderRepository evseOrderRepository;


    @Autowired
    private IotConfigProperties iotConfigProperties;

    @Autowired
    private OrderBizService orderBizService;

    @Autowired
    private PlugCache plugCache;


    @Override
    public EvseMessageType cmd() {
        return EvseMessageType.CHARGE_FROM_EVSE;
    }

    @Override
    public Mono<EvseTranx<Charge4EvseReq, Confirmation>> processRequest(BaseEvseMsgUp msgIn) {
        log.info("[{}] 桩端开始充电", msgIn.getBase().getTid());

        Charge4EvseReq req = (Charge4EvseReq) msgIn;
//        req.setBase(msg.getBaseMsg());

        OrderCreateData order = makeOrderData(req);

        return orderBizService.createOrderFromEvse(msgIn.getBase().getTid(), order)
            .map(response -> transformResponse(req, response));
    }

    private OrderCreateData makeOrderData(Charge4EvseReq req) {
        OrderCreateData order = new OrderCreateData();
        order.setEvseNo(req.getBase().getEvseNo());
        order.setPlugId(req.getBase().getPlugIdx());
        order.setStartType(req.getOrderStartType().getCode());
        order.setVin(req.getVin());
        order.setAccountNo(req.getAccount());

        OrderStopMode mode = new OrderStopMode();
        switch (req.getChargeMode()) {
            case FULL:
                mode.setType(FULL);
                mode.setAmount(new BigDecimal(0));
                break;

            case AMOUNT:
                mode.setType(ChargeMode.AMOUNT);
                mode.setAmount(new BigDecimal(req.getChargeQ()).divide(new BigDecimal(100), 4,
                    RoundingMode.HALF_UP));
                break;

            case KWH:
                mode.setType(ChargeMode.KWH);
                mode.setKwh(new BigDecimal(req.getChargeQ()).divide(new BigDecimal(100), 4,
                    RoundingMode.HALF_UP));
                break;

            case TIME:
                mode.setType(ChargeMode.TIME);
                mode.setTime((long) req.getChargeQ());
                break;
            default:
                break;
        }
        order.setStopMode(mode);
        return order;
    }

    private EvseTranx<Charge4EvseReq, Confirmation> transformResponse(Charge4EvseReq req,
        OrderCreateResponse response) {
        log.info("[{}] transformResponse. req: {}, response: {}", req.getBase().getTid(),
            req, response);

        IdTagInfo idTagInfo = new IdTagInfo(AuthCodeConverter.convert(response.getStatus()));
        StartTransactionConfirmation confirmation = new StartTransactionConfirmation();
        confirmation.setIdTagInfo(idTagInfo);
        if (response.getData() == null) {
            log.error("[{}] 创建订单失败！ ", req.getBase().getTid());
            return new EvseTranx<>(req.getBase().getEvseNo(), req, confirmation);
        }

        OrderCreateInfo orderInfo = response.getData();
        // log.info("traceId: {}, data: {}", msg.gettraceId(), orderInfo);

        orderInfo.setFrozenAmount(this.subtractDeposit(orderInfo)); // 扣减保证金

        Charge4EvseRes charge4EvseRes = new Charge4EvseRes();
        charge4EvseRes.setBase(req.getBase());
        charge4EvseRes.setOrderNo(orderInfo.getOrderNo());
        charge4EvseRes.setBalance(DecimalUtils.multiply100(orderInfo.getTotalAmount()));
        charge4EvseRes.setAmount(DecimalUtils.multiply100(orderInfo.getFrozenAmount()));
        charge4EvseRes.setStopMode(orderInfo.getStopMode());
        charge4EvseRes.setFeeDM(orderInfo.getCalcType().getCode());
        charge4EvseRes.setResult(ConstantCollections.OK);
        charge4EvseRes.setSoc(orderInfo.getLimitSoc());
        charge4EvseRes.setPrice(orderInfo.getPriceVo());
        charge4EvseRes.setPower(response.getPower());
        charge4EvseRes.setCarNo(StrUtil.rmChineseChar(orderInfo.getCarNo()));

        OrderData data = mapOrderData(req, charge4EvseRes);
        // 性能调整
        data.setAmount(orderInfo.getTotalAmount());
        data.setBalance(orderInfo.getFrozenAmount());
        evseOrderRepository.addOrder(data);

        confirmation.setTransactionId(data.getTransId());

        return new EvseTranx<>(req.getBase().getEvseNo(), req, confirmation);
    }

    private BigDecimal subtractDeposit(OrderCreateInfo data) {
        BigDecimal startOrderDeposit = iotConfigProperties.getStartOrderDeposit();
        BigDecimal frozenAmount = data.getFrozenAmount();
        if (//IotGwConstants.PAY_ACCOUNT_TYPE_PREPAY.equals(orderData.getAccountType())
            //&&
            DecimalUtils.gtZero(startOrderDeposit)) {
            BigDecimal amountX = frozenAmount.subtract(startOrderDeposit);
            if (DecimalUtils.gtZero(amountX)) {
                frozenAmount = amountX;
                log.info("订单扣除保证金 {} 后，可充电金额变更为 {}", startOrderDeposit,
                    frozenAmount);
            }
        }
        return frozenAmount;
    }

    private OrderData mapOrderData(Charge4EvseReq req, Charge4EvseRes response) {
        log.info("[{}] mapOrderData. response: {}", req.getBase().getTid(), response);

        OrderData data = new OrderData();

//        data.setSeq(response.getSeq());//云端返回请求消息中的seq
//        data.setStatus(AuthCodeConverter.convert(response.getResult()));
        String transactionId = response.getOrderNo().substring(5);
        data.setTransId(Integer.valueOf(transactionId));
        data.setOrderNo(response.getOrderNo());
        data.setOrderStatus(ChargeOrderStatus.INIT);
//        data.setBalance(DecimalUtils.divide100(response.getData().getBalance()));
//        data.setAmount(DecimalUtils.divide100(response.getData().getAmount()));
        data.setStopMode(response.getStopMode());

        // 订单创建时无以下字段
        data.setStartType(req.getOrderStartType().getCode());
        // data.setPlugId(decoder.getPlugNo());
        // data.setEvseId(decoder.getEvseNo());
        // data.setAccountNo(decoder.getAccountNo());
        data.setAccount(req.getAccount());
        // data.setVin(decoder.getAccountNo());
        // data.setSoc(decoder.getSoc());
        // data.setPriceCode("");
        data.setStartTime(req.getStartTime());
        data.setStartMeter(req.getStartMeter());

        data.setEvseNo(req.getBase().getEvseNo());
        data.setIdx(req.getBase().getPlugIdx());

        log.info("[{}] map data: {}", req.getBase().getTid(), data);

        return data;
    }


}
