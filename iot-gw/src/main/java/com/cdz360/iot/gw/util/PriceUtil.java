package com.cdz360.iot.gw.util;

import org.apache.commons.lang3.tuple.Pair;

public class PriceUtil {

    /**
     * 检查时间字符串格式并转换
     * <p>如果格式是"HHmm"，则转换为"HH:mm"</p>
     * <p>如果已经是"HH:mm"格式，则直接返回</p>
     *
     * @param timeStr 时间字符串
     * @return 格式化后的时间字符串
     */
    public static String formatTimeString(String timeStr) {
        if (timeStr == null || timeStr.isEmpty()) {
            return timeStr;
        }

        // 已经是HH:mm格式
        if (timeStr.contains(":")) {
            return timeStr;
        }

        // 尝试将HHmm转换为HH:mm
        if (timeStr.matches("\\d{3,4}")) {
            // 补齐4位数
            String paddedTime = String.format("%04d", Integer.parseInt(timeStr));
            return paddedTime.substring(0, 2) + ":" + paddedTime.substring(2, 4);
        }

        // 其他情况，返回原字符串
        return timeStr;
    }

    public static Pair<Integer, Integer> splitTimeStr(String timeStr) {
        String str = formatTimeString(timeStr);
        return Pair.of(Integer.valueOf(str.substring(0, 2)), Integer.valueOf(str.substring(3, 5)));
    }

}
