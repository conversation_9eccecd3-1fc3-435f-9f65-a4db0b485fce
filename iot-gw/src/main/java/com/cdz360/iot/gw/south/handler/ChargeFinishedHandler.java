package com.cdz360.iot.gw.south.handler;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.biz.ds.EvseOrderRepository;
import com.cdz360.iot.gw.core.biz.OrderBizService;
import com.cdz360.iot.gw.model.IotPlatformResponse;
import com.cdz360.iot.gw.model.evse.protocol.ChargeStoppedUp;
import com.cdz360.iot.gw.model.evse.protocol.EvseTranx;
import com.cdz360.iot.gw.north.model.OrderData;
import com.cdz360.iot.gw.north.model.OrderDetail;
import com.cdz360.iot.gw.north.model.OrderStopData;
import com.cdz360.iot.gw.north.model.OrderTimeIntervalDetail;
import com.cdz360.iot.gw.north.model.OrderUpdateData;
import com.cdz360.iot.gw.north.server.NorthOrderService;
import com.cdz360.iot.gw.util.DecimalUtils;
import com.cdz360.iot.gw.util.OrderNoUtils;
import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.model.core.AuthorizationStatus;
import eu.chargetime.ocpp.model.core.IdTagInfo;
import eu.chargetime.ocpp.model.core.StopTransactionConfirmation;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class ChargeFinishedHandler extends UpstreamHandler2 {

    @Autowired
    private EvseOrderRepository evseOrderRepository;

    @Autowired
    private NorthOrderService orderService;

    @Autowired
    private OrderBizService orderBizService;

    @Override
    public EvseMessageType cmd() {
        return EvseMessageType.CHARGE_FINISHED;
    }

    @Override
    public Mono<EvseTranx<ChargeStoppedUp, Confirmation>> processRequest(BaseEvseMsgUp msgIn) {
        String tid = msgIn.getBase().getTid();
        ChargeStoppedUp req = (ChargeStoppedUp) msgIn;
        log.info("[{}] 桩上报订单结束. req: {}", tid, JsonUtils.toJsonString(req));

        /*
        //奥特迅桩上报的订单数据存在总电量有，但分时电量为0的情况，需要做特殊处理
        this.fixAtxOrder(msgIn.getBase().getTid(), req, msgIn.getBase().getEvseNo());

        //奥特迅桩的计费只支持2位小数，需要根据云端获取到的计费信息重新修正金额
        this.fixAtxOrder2(msgIn.getBase().getTid(), req, msgIn.getBase().getEvseNo());
        */

        if (StringUtils.isBlank(req.getOrderNo())) {
            // 万一订单号丢了......此处做个补救措施,避免丢单
            req.setOrderNo(OrderNoUtils.genOrderNo4Abnormal(req.getBase().getEvseNo()));
        }

        //判断缓存中该订单是否存在，存在以缓存中 启动方式为准；不存在则认为是紧急卡/无卡启动
        OrderData order = evseOrderRepository.getOrder(req.getOrderNo());

        // if (order == null) {
        //     throw new IllegalArgumentException("订单不存在：" + req.getOrderNo());
        // }

        StopTransactionConfirmation confirmation = new StopTransactionConfirmation();
        IdTagInfo idTagInfo = new IdTagInfo(AuthorizationStatus.Accepted);
        confirmation.setIdTagInfo(idTagInfo);

        //此订单已经停止过了,不能再次上报
        if (order != null && order.getNeedDel() == 1) {
            log.error("[{}] 此订单已经停止过了，不能重复上报。 orderNo: {},",
                tid, order.getOrderNo());

            return Mono.just(new EvseTranx<>(req.getBase().getEvseNo(), req, confirmation));
        } else if (null != order) {
            order.setNeedDel(1);
        }

        if (order != null) {
            log.info("[{}] 订单存在。 order: {}", tid, order);

            //分时段详情,在调用stop前先调用update
            List<OrderTimeIntervalDetail> orderTimeIntervalDetails = order.getDetails();
            if (!CollectionUtils.isEmpty(orderTimeIntervalDetails)) {

                OrderUpdateData updateReq = orderService.transOrderUpdateRequest(
                    req.getBase().getEvseNo(), req.getBase().getPlugIdx(), order);
                orderService.updateOrder(tid, updateReq).subscribe(a -> {
                    //上报后清掉分时段详情
                    order.getDetails().clear();
                    //更新到缓存
                    evseOrderRepository.updateOrder(order);
                });
            }
        }

        OrderStopData orderStopRequest = makeRequest(tid, req, order);

        orderBizService.checkOrderFee(tid, orderStopRequest)   // 检查桩的计费数据
            .flatMap(res1 -> orderBizService.orderStopped(tid, res1))
            .doOnNext(response -> {
                if (response == null || response.getStatus() != 0) {
                    if (null != order) {
                        order.setNeedDel(0);
                    }
                    log.error("[{}] 停止订单订单失败！ request: {}", tid,
                        orderStopRequest);
                    throw new RuntimeException("上传充电结束失败");
                }
            })
            .doOnError(throwable -> Mono.just(new IotPlatformResponse<>()))
            .doOnNext(response -> {
                //成功后更新订单的待删除标记
                evseOrderRepository.updateToNeedDelFlag(orderStopRequest.getOrderNo());
            }).subscribe();

        return Mono.just(new EvseTranx<>(req.getBase().getEvseNo(), req, confirmation));
    }

    private OrderStopData makeRequest(String traceId, ChargeStoppedUp req, OrderData order) {
        log.info("[{}] 充电结束上报请求。request: {}, order: {}", traceId, req, order);

        // 分时电费
        List<OrderDetail> details = req.getDetail();

        BigDecimal totalElecFee = req.getElecFee();
        BigDecimal totalServFee = req.getServFee();

        OrderStopData orderStopRequest = new OrderStopData();
        // orderStopRequest.setSeq(SeqGeneratorUtil.newStringId());
        // orderStopRequest.setOrderNo(orderNo); // 下面会重新赋值
        orderStopRequest.setEvseNo(req.getBase().getEvseNo());
        orderStopRequest.setPlugId(req.getBase().getPlugIdx());

        orderStopRequest.setCompleteCode(req.getCompleteCode());// 订单完成原因
        orderStopRequest.setStopCode(
            req.getErrorCode());// 故障码(订单完成原因)。成功为00（无论是交流还是直流），直流的故障码直接传桩端给的值，交流的加1000后再上报给平台

        orderStopRequest.setStartMeter(req.getStartMeter());//充电开始前电表读数
        orderStopRequest.setStopMeter(req.getStopMeter());//充电完成后电表读数
        // 订单总电量
        orderStopRequest.setKwh(req.getKwh());
        orderStopRequest.setSecondPlugIdx(req.getSecondPlugIdx());
        orderStopRequest.setSecondStartMeter(req.getSecondStartMeter());
        orderStopRequest.setSecondStopMeter(req.getSecondStopMeter());
        orderStopRequest.setSecondKwh(req.getSecondKwh());
        //订单总金额
        orderStopRequest.setElecFee(totalElecFee);
        //订单总电费
        //订单总电费的字段？
        //订单总服务费
        orderStopRequest.setServFee(totalServFee);

        orderStopRequest.setStartTime(req.getStartTime());
        orderStopRequest.setStopTime(req.getStopTime());
        orderStopRequest.setStartType(req.getOrderStartType());
        orderStopRequest.setAccountNo(req.getAccount());
        orderStopRequest.setVin(req.getVin());
        orderStopRequest.setMinBatteryVoltage(req.getMinBatteryVoltage());

        orderStopRequest.setStartSoc(req.getStartSoc());
        orderStopRequest.setSoc(req.getStopSoc());

        if (req.getPriceCode() != null) {
            orderStopRequest.setPriceCode(req.getPriceCode());
        }
        orderStopRequest.setDetail(details);

        if (order != null) {
            orderStopRequest.setOrderNo(order.getOrderNo());
            orderStopRequest.setStartType(OrderStartType.valueOf(order.getStartType() & 0xFF));
            if (!NumberUtils.gtZero(orderStopRequest.getPlugId())) {
                orderStopRequest.setPlugId(order.getIdx());
            }
            if (orderStopRequest.getStartMeter() == null) {
                orderStopRequest.setStartMeter(order.getStartMeter());
            }
            if (orderStopRequest.getStopMeter() == null) {
                orderStopRequest.setStopMeter(order.getCurrMeter());
            }
            if (!NumberUtils.gtZero(orderStopRequest.getStartTime())) {
                orderStopRequest.setStartTime(order.getStartTime());
            }
            if (StringUtils.isBlank(orderStopRequest.getAccountNo())
                && StringUtils.isNotBlank(order.getAccount())) {
                orderStopRequest.setAccountNo(order.getAccount());
            }
            if (!NumberUtils.gtZero(orderStopRequest.getPriceCode())
                && order.getPriceCode() != null) {
                orderStopRequest.setPriceCode(order.getPriceCode());
            }

            if (CollectionUtils.isEmpty(orderStopRequest.getDetail())) {

                // 将最近的时段账单的结束时间补全
                OrderDetail lastOrderDetail = order.getLastOrderDetail();
                if (lastOrderDetail != null) {
                    if (NumberUtils.isZero(lastOrderDetail.getStopTime())) {
                        lastOrderDetail.setStopTime(req.getStopTime());
                    }

                    // 最后这个时段的电量可能会有误差，要重新计算
                    BigDecimal currKwh = orderStopRequest.getStopMeter()
                        .subtract(lastOrderDetail.getStartMeter());
                    BigDecimal elecPrice = lastOrderDetail.getElecPrice().multiply(currKwh)
                        .setScale(4, RoundingMode.HALF_UP);
                    BigDecimal servPrice = lastOrderDetail.getServPrice().multiply(currKwh)
                        .setScale(4, RoundingMode.HALF_UP);
                    lastOrderDetail.setKwh(currKwh)
                        .setElecFee(elecPrice)
                        .setServFee(servPrice);

                    order.pushBillingDetail(lastOrderDetail);
                }

                if (order.getBillingDetails() != null && !order.getBillingDetails().isEmpty()) {
                    orderStopRequest.setDetail(
                        order.getBillingDetails().values().stream().toList());
                }

                if (CollectionUtils.isNotEmpty(orderStopRequest.getDetail())) {
                    if (orderStopRequest.getKwh() == null || DecimalUtils.isZero(
                        orderStopRequest.getKwh())) {
                        orderStopRequest.setKwh(orderStopRequest.getDetail().stream()
                            .map(OrderDetail::getKwh)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                    if (orderStopRequest.getElecFee() == null || DecimalUtils.isZero(
                        orderStopRequest.getElecFee())) {
                        orderStopRequest.setElecFee(orderStopRequest.getDetail().stream()
                            .map(OrderDetail::getElecFee)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                    if (orderStopRequest.getServFee() == null || DecimalUtils.isZero(
                        orderStopRequest.getServFee())) {
                        orderStopRequest.setServFee(orderStopRequest.getDetail().stream()
                            .map(OrderDetail::getServFee)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                }

            }
        } else {
            orderStopRequest.setOrderNo(req.getOrderNo());
        }

        log.info("[{}] 充电结束上报请求。orderStopRequest: {}", traceId, orderStopRequest);

        return orderStopRequest;
    }

}
