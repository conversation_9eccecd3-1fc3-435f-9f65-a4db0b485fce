package com.cdz360.iot.gw.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import org.springframework.data.util.Pair;

public class OrderNoGenerator {

    private static final Random RANDOM = new Random();

    /**
     * 生成20位订单号 格式：
     * <p>前2位: 固定前缀(42)</p>
     * <p>接下来10位: 年月日时分</p>
     * <p>接下来4位: 序列号</p>
     * <p>最后4位: 随机字母数字组合</p>
     */
    public static Pair<String, Integer> generateOrderNumber() {
        StringBuilder sb = new StringBuilder();

        // 1. 添加固定前缀
        sb.append("42");

        // 2. 添加日期（年月日时分）
        LocalDateTime now = LocalDateTime.now();
        String dateStr = now.format(DateTimeFormatter.ofPattern("yyMMddHHmm"));
        sb.append(dateStr);

        // 3. 添加业务代码（示例使用1403）
        // sb.append("1403");

        // 4. 添加4位序列号
        String sequence = String.format("%04d", RANDOM.nextInt(10000));
        sb.append(sequence);

        // 5. 添加4位随机字母数字组合
        String randomEnd = generateRandomHex(4);
        sb.append(randomEnd);

        // TODO: 2025/4/25 WZFIX 获取redis中的orderNo作为后半部分
        String orderNo = sb.toString();
        Integer transId = Integer.valueOf(orderNo.substring(7, 16)); // "dHHmm" + 4个random
        return Pair.of(orderNo, transId);
    }

    public static Integer orderNo2TransId(String orderNo) {
        String str = orderNo.substring(5); // "dHHmm" + 4个random
        return Integer.valueOf(str);
    }

    /**
     * 生成指定长度的随机十六进制字符串
     */
    private static String generateRandomHex(int length) {
        StringBuilder sb = new StringBuilder();
//        String chars = "0123456789ABCDEF";
        String chars = "0123456789";
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(RANDOM.nextInt(chars.length())));
        }
        return sb.toString();
    }

    public static void main(String[] args) throws InterruptedException {
        // 测试生成10个订单号
        for (int i = 0; i < 10; i++) {
            Thread.sleep(3000);
            Pair<String, Integer> pair = generateOrderNumber();
            System.out.println(pair.getFirst());
            System.out.println(pair.getSecond());
        }
    }

}
