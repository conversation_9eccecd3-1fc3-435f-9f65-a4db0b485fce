package com.cdz360.iot.gw.config.ws.profile;

import com.cdz360.iot.gw.biz.UpgradeProcess;
import eu.chargetime.ocpp.feature.profile.ServerFirmwareManagementEventHandler;
import eu.chargetime.ocpp.feature.profile.ServerFirmwareManagementProfile;
import eu.chargetime.ocpp.model.firmware.DiagnosticsStatusNotificationConfirmation;
import eu.chargetime.ocpp.model.firmware.DiagnosticsStatusNotificationRequest;
import eu.chargetime.ocpp.model.firmware.FirmwareStatusNotificationConfirmation;
import eu.chargetime.ocpp.model.firmware.FirmwareStatusNotificationRequest;
import java.util.UUID;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Slf4j
public class ServerFirmwareManagementProfileConfig {

    @Autowired
    private UpgradeProcess upgradeProcess;

    @Bean
    public ServerFirmwareManagementEventHandler getFirmwareManagementEventHandler() {
        return new ServerFirmwareManagementEventHandler() {
            @Override
            public DiagnosticsStatusNotificationConfirmation handleDiagnosticsStatusNotificationRequest(
                UUID sessionIndex, DiagnosticsStatusNotificationRequest request) {
                String tid = request.getBase().getTid();
                String evseNo = request.getBase().getEvseNo();
                log.info("[{} {}] handleDiagnosticsStatusNotificationRequest. request: {}", tid,
                    evseNo, request);
                // ... handle event

                return null; // returning null means unsupported feature
            }

            @Override
            public FirmwareStatusNotificationConfirmation handleFirmwareStatusNotificationRequest(
                UUID sessionIndex, FirmwareStatusNotificationRequest request) {
                String tid = request.getBase().getTid();
                String evseNo = request.getBase().getEvseNo();
                log.info("[{} {}] handleFirmwareStatusNotificationRequest. request: {}", tid,
                    evseNo, request);
                return (FirmwareStatusNotificationConfirmation) upgradeProcess.reportUpgradeResultByFirmwareStatus(
                    request); // returning null means unsupported feature
            }

        };
    }

    @Bean
    public ServerFirmwareManagementProfile createFirmwareManagement(
        ServerFirmwareManagementEventHandler firmwareManagementEventHandler) {
        return new ServerFirmwareManagementProfile(firmwareManagementEventHandler);
    }
}
