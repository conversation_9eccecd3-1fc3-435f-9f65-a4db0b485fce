package com.cdz360.iot.gw.south.server;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.model.GwEvseVo;
import eu.chargetime.ocpp.model.core.KeyValueType;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ConfigurationService {

    private static final List<String> BASE_SAMPLED_DATA_LIST = List.of(
        IotGwConstants.CURRENT_IMPORT, IotGwConstants.POWER_ACTIVE_IMPORT, IotGwConstants.VOLTAGE,
        IotGwConstants.ENERGY_ACTIVE_IMPORT_INTERVAL);

    @Autowired
    private JsonServerImpl jsonServer;

    @Autowired
    private EvseRepository evseRepository;

    /**
     * 检查MeterValues.req中的采样测量值
     *
     * @param msg
     */
    public void checkEvseMeterValuesSampledData(BaseEvseMsgUp msg) {
        EvseMsgBase base = msg.getBase();
        String tid = base.getTid();
        String evseNo = base.getEvseNo();

        String targetKey = IotGwConstants.METER_VALUES_SAMPLED_DATA;
        Optional<KeyValueType[]> configResOpt = jsonServer.sendGetConfigurationReq(base,
            List.of(targetKey));

        if (configResOpt.isEmpty()) {
            log.info("[{} {}] 获取桩Configuration失败，跳过检查. targetKey: {}", tid, evseNo,
                targetKey);
            return;
        }
        GwEvseVo evse = evseRepository.getEvse(evseNo);

        List<String> expectedSampledDatas = new ArrayList<>(BASE_SAMPLED_DATA_LIST);
        if (SupplyType.DC.equals(evse.getSupplyType())) {
            expectedSampledDatas.add(IotGwConstants.TEMPERATURE);
            expectedSampledDatas.add(IotGwConstants.SoC);
        }

        List<String> finalSampledDatas;
        Optional<List<String>> currSampledDataOpt = Stream.of(configResOpt.get())
            .filter(e -> targetKey.equals(e.getKey()) && StringUtils.isNotBlank(e.getValue()))
            .findFirst()
            .map(e -> Arrays.stream(e.getValue().split(",")).collect(Collectors.toList()));
        if (currSampledDataOpt.isPresent()) {
            List<String> currSampledData = currSampledDataOpt.get();
            if (new HashSet<>(currSampledData).containsAll(expectedSampledDatas)) {
                // 已符合期望，无需变更
                return;
            }
            finalSampledDatas = new ArrayList<>(currSampledData);
            expectedSampledDatas.forEach(e -> {
                if (!finalSampledDatas.contains(e)) {
                    finalSampledDatas.add(e);
                }
            });
        } else {
            finalSampledDatas = expectedSampledDatas;
        }

        if (CollectionUtils.isNotEmpty(finalSampledDatas)) {
            log.info("[{} {}] 需要变更桩Configuration, finalSampledDatas.size: {}", tid, evseNo,
                finalSampledDatas.size());
            jsonServer.sendChangeConfigurationReq(base, targetKey,
                String.join(",", finalSampledDatas));
        }

    }

}
