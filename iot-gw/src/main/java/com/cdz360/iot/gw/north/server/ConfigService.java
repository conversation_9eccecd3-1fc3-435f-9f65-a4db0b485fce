package com.cdz360.iot.gw.north.server;

import static com.cdz360.iot.gw.util.WebClientUtil.asyncPost;

import com.cdz360.iot.gw.config.ApiUrl;
import com.cdz360.iot.gw.model.base.BasePlatformResponse;
import com.cdz360.iot.gw.model.evse.protocol.CfgReportUp;
import com.cdz360.iot.gw.north.model.ConfigInfo;
import com.cdz360.iot.gw.north.model.ConfigInfoRequest;
import com.cdz360.iot.gw.north.model.ConfigModifyResult;
import com.cdz360.iot.gw.north.model.EvseCfgGetRequest;
import com.cdz360.iot.gw.north.model.EvseCfgGetResponse;
import com.cdz360.iot.gw.north.model.EvseCfgResultRequest;
import com.cdz360.iot.gw.north.model.EvseGetPriceCfgResponse;
import com.cdz360.iot.gw.north.model.GetConfig;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
public class ConfigService {

    private final Logger logger = LoggerFactory.getLogger(ConfigService.class);

    @Autowired
    private IotClient iotClient;

    public EvseCfgGetResponse evseCfgGet(String traceId, GetConfig config) {
        logger.info("获取桩配置信息。traceId: {}, request: {}", traceId, config);

        EvseCfgGetRequest request = new EvseCfgGetRequest();
        request.setSeq(SeqGeneratorUtil.newStringId());
        request.setData(config);

        EvseCfgGetResponse ret = iotClient.sendMsgPostByUrl(request, ApiUrl.URL_CFG_EVSE,
            EvseCfgGetResponse.class);

        logger.info("获取桩配置. traceId: {}, response: {}", traceId, ret);

        return ret;
    }

    public void queryConfigInfo(String tid, ConfigInfo request) {
        logger.info("[{}] 云端获取桩端配置。 request: {}", tid, request);

        ConfigInfoRequest configInfoRequest = new ConfigInfoRequest();
        configInfoRequest.setSeq(SeqGeneratorUtil.newStringId());
        List<ConfigInfo> list = new ArrayList<>();
        list.add(request);
        configInfoRequest.setData(list);

        logger.info("[{}] 上传桩配置信息. request: {}", tid, configInfoRequest);

        BasePlatformResponse ret = iotClient.sendMsgPostByUrl(configInfoRequest,
            ApiUrl.URL_CFG_EVSE_INFO, BasePlatformResponse.class);

        logger.info("[{}] 上传桩配置信息. response: {}", tid, ret);
    }


    /**
     * 从云端获取桩的计费配置信息
     */
    public Mono<EvseGetPriceCfgResponse> getPriceCfg(String tid, GetConfig config) {
        logger.info("[{}] 向云端请求桩计费信息. request: {}", tid, config);

        EvseCfgGetRequest request = new EvseCfgGetRequest();
        request.setSeq(SeqGeneratorUtil.newStringId());
        request.setData(config);

        return asyncPost(ApiUrl.URL_GET_PRICE, request, EvseGetPriceCfgResponse.class).doOnNext(
            cloudRes -> {
                if (cloudRes == null || cloudRes.getStatus() != 0) {
                    logger.error("[{}] 从云端获取桩计费配置信息失败。 response: {}", tid, cloudRes);
                } else {
                    logger.info("[{}] 从云端获取桩计费配置信息。 response: {}", tid, cloudRes);
                }
                if (cloudRes == null) {
                    logger.error("[{}] 从云端获取桩计费配置信息失败。 response: {}", tid, cloudRes);
                }
            }).doOnError(throwable -> {
            logger.error("[{}] 从云端获取桩计费配置信息失败。 request: {}", tid, request, throwable);
        });
    }

    //上报配置结果
    public void reportModifyResult(String traceId, ConfigModifyResult result) {
        logger.info("桩配置结果上报。traceId: {}, request: {}", traceId, result);

        EvseCfgResultRequest request = new EvseCfgResultRequest();
        request.setSeq(SeqGeneratorUtil.newStringId());
        request.setData(result);

        BasePlatformResponse ret = iotClient.sendMsgPostByUrl(request, ApiUrl.URL_CFG_EVSE_RESULT,
            BasePlatformResponse.class);

        logger.info("桩配置结果上报。traceId: {}, response：{}", traceId, ret);

        // // 处理返回的响应消息
        // if (ret == null) {
        //     // 网络异常
        //     filter.callback(JsonUtils.toJsonString(configResult));
        // }

        // logger.info("桩配置更新结果上报。request: {}, response: {}", request, response);
    }

    /**
     * 上报配置变更
     */
    public BasePlatformResponse reportConfigChange(String traceId, CfgReportUp uMsg) {
        logger.info("上报桩端配置变更。traceId: {}, uMsg: {}", traceId, uMsg);

        ConfigInfoRequest configInfoRequest = new ConfigInfoRequest();
        configInfoRequest.setSeq(SeqGeneratorUtil.newStringId());
        List<ConfigInfo> list = new ArrayList<>();

        ConfigInfo cfg = new ConfigInfo();
        cfg.setBmsVer(uMsg.getBmsVer())
            .setAutoStop(uMsg.getAutoStop())
            .setBalanceMode(uMsg.getBalanceMode())
            .setCombination(uMsg.getCombination())
            .setHeating(uMsg.getHeating())
            .setHeatingVoltage(uMsg.getHeatingVoltage())
            .setBatteryCheck(uMsg.getBatteryCheck())
            .setIsolation(uMsg.getIsolation())
            .setManualMode(uMsg.getManualMode());

        list.add(cfg);
        configInfoRequest.setData(list);

        logger.info("上传桩配置信息. request: {}", configInfoRequest);

        BasePlatformResponse ret = iotClient.sendMsgPostByUrl(configInfoRequest,
            ApiUrl.URL_CFG_EVSE_INFO, BasePlatformResponse.class);

        logger.info("上报桩端配置变更。traceId: {}, response: {}", traceId, ret);

        return ret;
    }
}
