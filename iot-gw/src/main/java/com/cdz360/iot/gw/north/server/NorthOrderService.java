package com.cdz360.iot.gw.north.server;

import static com.cdz360.iot.gw.util.WebClientUtil.asyncPost;

import com.cdz360.iot.gw.biz.ds.RedisRwService;
import com.cdz360.iot.gw.config.ApiUrl;
import com.cdz360.iot.gw.north.model.OrderCreateData;
import com.cdz360.iot.gw.north.model.OrderCreateRequest;
import com.cdz360.iot.gw.north.model.OrderCreateResponse;
import com.cdz360.iot.gw.north.model.OrderData;
import com.cdz360.iot.gw.north.model.OrderFeeRefreshData;
import com.cdz360.iot.gw.north.model.OrderFeeRefreshRequest;
import com.cdz360.iot.gw.north.model.OrderFeeRefreshResponse;
import com.cdz360.iot.gw.north.model.OrderResponse;
import com.cdz360.iot.gw.north.model.OrderStartData;
import com.cdz360.iot.gw.north.model.OrderStartRequest;
import com.cdz360.iot.gw.north.model.OrderStartingData;
import com.cdz360.iot.gw.north.model.OrderStartingRequest;
import com.cdz360.iot.gw.north.model.OrderStopData;
import com.cdz360.iot.gw.north.model.OrderStopRequest;
import com.cdz360.iot.gw.north.model.OrderUpdateData;
import com.cdz360.iot.gw.north.model.OrderUpdateRequest;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class NorthOrderService {

    @Autowired
    private RedisRwService redisRwService;


    public Mono<OrderCreateResponse> createOrder(String traceId, OrderCreateData data) {
        // log.info("创建订单。traceId: {}, data: {}", traceId, data);

        OrderCreateRequest request = new OrderCreateRequest();
        request.setSeq(SeqGeneratorUtil.newStringId());
        request.setData(data);

        log.info("[{} {}] 请求创建订单, request: {}", traceId, data.getEvseNo(), request);
        return asyncPost(ApiUrl.URL_CREATE_ORDER, request, OrderCreateResponse.class)
            .doOnNext(r -> {
                if (r == null || r.getStatus() != 0) {
                    // throw new RuntimeException("创建订单失败");//不要抛出自定义的运行时异常，会破坏后面的调用链
                    log.error("[{} {}] 创建订单失败。 response: {}", traceId, data.getEvseNo(), r);
                } else {
                    log.info("[{} {}] 创建订单返回： response: {}", traceId, data.getEvseNo(), r);
                }

                if (r == null) {
                    // filter.callback(JsonUtils.toJsonString(request));
                    log.error("[{} {}] 创建订单失败。 response: is null", traceId, data.getEvseNo());
                }

            }).doOnError(throwable -> {
                log.error("[{} {}] 创建订单失败。 request:{}", traceId, data.getEvseNo(), request,
                    throwable);
                // filter.callback(JsonUtils.toJsonString(request));
            });

    }

    public Mono<OrderResponse> startResult(String traceId, String evseNo, int plugIdx,
        OrderStartingData data) {
        // log.info("开始订单中。request: {}", request);
        OrderStartingRequest request = new OrderStartingRequest();
        request.setSeq(SeqGeneratorUtil.newStringId());
        request.setData(data);

        log.info("[{} {} {}] 充电开始中状态上报。 request: {}", traceId, evseNo, plugIdx, request);

        return asyncPost(ApiUrl.URL_START_RESULT, request, OrderResponse.class).doOnNext(
            orderResponse -> {

                if (orderResponse == null || orderResponse.getStatus() != 0) {
                    // throw new RuntimeException("订单上报失败");//不要抛出自定义的运行时异常，会破坏后面的调用链
                    log.error("[{} {} {}] 订单上报失败。response: {}", traceId, evseNo, plugIdx,
                        orderResponse);
                } else {
                    log.info("[{} {} {}] 开始订单上报。 response: {}", traceId, evseNo, plugIdx,
                        orderResponse);
                }

                if (orderResponse == null) {
                    // filter.callback(JsonUtils.toJsonString(request));
                    log.error("[{} {} {}] 订单上报失败。response: {}", traceId, evseNo, plugIdx,
                        orderResponse);
                }

            }).doOnError(throwable -> {
            log.error("[{} {} {}] 订单上报失败。 request: {}", traceId, evseNo, plugIdx, request,
                throwable);
            // filter.callback(JsonUtils.toJsonString(request));
        });

    }

    public Mono<OrderResponse> startOrder(String traceId, OrderStartData data) {

        // log.info("开启订单。traceId: {}", traceId);

        OrderStartRequest request = new OrderStartRequest();
        request.setSeq(SeqGeneratorUtil.newStringId());
        request.setData(data);

        log.info("[{} {} {}] 订单开启。 request: {}", traceId, data.getEvseNo(), data.getPlugId(),
            request);

        return asyncPost(ApiUrl.URL_START_ORDER, request, OrderResponse.class).doOnNext(
            orderResponse -> {

                if (orderResponse == null || orderResponse.getStatus() != 0) {
                    // throw new RuntimeException("开始订单失败");//不要抛出自定义的运行时异常，会破坏后面的调用链
                    log.error("[{} {} {}]开始订单失败。 request = {}, response: {}", traceId,
                        data.getEvseNo(), data.getPlugId(), request, orderResponse);
                } else {
                    log.info("[{} {} {}]开始订单。 request = {}, response: {}", traceId,
                        data.getEvseNo(), data.getPlugId(), request, orderResponse);
                }

                if (orderResponse == null) {
                    // filter.callback(JsonUtils.toJsonString(orderStartRequest));
                    log.error("[{} {} {}]开始订单失败。 response is null",
                        traceId, data.getEvseNo(),
                        data.getPlugId());
                }

            }).doOnError(throwable -> {
            log.error("[{} {} {}] 开始订单失败。 request: {}", traceId, data.getEvseNo(),
                data.getPlugId(), request, throwable);
            // filter.callback(JsonUtils.toJsonString(orderStartRequest));
        });
    }

    public Mono<OrderResponse> stopOrder(String traceId, OrderStopData data) {

        // log.info("停止订单。request: {}", request);
        OrderStopRequest request = new OrderStopRequest();
        request.setSeq(SeqGeneratorUtil.newStringId());
        request.setData(data);

        log.info("[{} {} {}] 停止订单。request: {}", traceId, data.getEvseNo(),
            data.getPlugId(), request);

        return asyncPost(ApiUrl.URL_STOP_ORDER, request, OrderResponse.class).doOnNext(
            orderResponse -> {

                if (orderResponse == null || orderResponse.getStatus() != 0) {
                    // throw new RuntimeException("停止订单失败");//不要抛出自定义的运行时异常，会破坏后面的调用链
                    //放入redis中待人工手动处理
                    redisRwService.setOfflineOrderStopRequest(request);
                    log.error("[{} {} {}] 停止订单失败。 response: {}", traceId, data.getEvseNo(),
                        data.getPlugId(), orderResponse);
                } else {
                    log.info("[{} {} {}] 停止订单。 response: {}", traceId, data.getEvseNo(),
                        data.getPlugId(), orderResponse);
                }

                if (orderResponse == null) {
                    // filter.callback(JsonUtils.toJsonString(request));
                    log.error("[{} {} {}] 停止订单失败。 response: {}", traceId, data.getEvseNo(),
                        data.getPlugId(), orderResponse);
                }

            }).doOnError(throwable -> {
            log.error("[{} {} {}] 停止订单失败。 request: {}", traceId, data.getEvseNo(),
                data.getPlugId(), request, throwable);
            // filter.callback(JsonUtils.toJsonString(request));
        });
    }

    public Mono<OrderResponse> updateOrder(String traceId, OrderUpdateData data) {
        // log.info("更新订单。request: {}", request);

        OrderUpdateRequest request = new OrderUpdateRequest();
        request.setSeq(SeqGeneratorUtil.newStringId());
        request.setData(data);

        log.info("[{} {} {}] 充电中订单信息上报。 seq = {}, evseNo = {}, orderNo = {}",
            traceId, data.getEvseNo(), data.getPlugId(), request.getSeq(), data.getEvseNo(),
            data.getOrderNo());

        // String json = JsonUtils.toJsonString(request);

        return asyncPost(ApiUrl.URL_UPDATE_ORDER, request, OrderResponse.class)
            .doOnNext(orderResponse -> {
                if (orderResponse == null || orderResponse.getStatus() != 0) {
                    // throw new RuntimeException("订单状态更新失败");//不要抛出自定义的运行时异常，会破坏后面的调用链
                    log.error("[{} {} {}] 订单状态更新失败。 response: {}",
                        traceId, data.getEvseNo(), data.getPlugId(), orderResponse);
                    // filter.callback(json);
                } else {
                    log.info("[{} {} {}] 订单状态更新。 response: {}",
                        traceId, data.getEvseNo(), data.getPlugId(), orderResponse);
                }

            }).doOnError(throwable -> {
                log.error("[{} {} {}] 订单状态更新失败。 request: {}",
                    traceId, data.getEvseNo(), data.getPlugId(), request, throwable);
                // filter.callback(json);
            });
    }

    public Mono<OrderFeeRefreshResponse> orderFeeRefresh(String traceId, OrderFeeRefreshData data) {
        // log.info("在线订单续费请求: request={}", refreshRequest);

        OrderFeeRefreshRequest request = new OrderFeeRefreshRequest();
        request.setSeq(SeqGeneratorUtil.newStringId());
        request.setData(data);

        log.info("[{} {} {}] 订单续费。request={}", traceId, data.getEvseNo(), data.getPlugId(),
            request);

        return asyncPost(ApiUrl.URL_ORDER_FEE_REFRESH, request, OrderFeeRefreshResponse.class)
            .doOnNext(orderResponse -> {

                if (orderResponse == null || orderResponse.getStatus() != 0) {
                    // throw new RuntimeException("订单续费失败");//不要抛出自定义的运行时异常，会破坏后面的调用链
                    log.error("[{} {} {}] 订单续费失败。 response: {}",
                        traceId, data.getEvseNo(), data.getPlugId(), orderResponse);
                } else {
                    log.info("[{} {} {}] 订单续费成功。 response: {}",
                        traceId, data.getEvseNo(), data.getPlugId(), orderResponse);
                }

            }).doOnError(throwable -> {
                // filter.callback(JsonUtils.toJsonString(refreshRequest)); // 网络异常
                log.error("[{} {} {}] 订单续费失败。 request: {}",
                    traceId, data.getEvseNo(), data.getPlugId(), request, throwable);
            });
    }

    public OrderUpdateData transOrderUpdateRequest(String evseNo, int plugId, OrderData orderData) {

//        String json = JsonUtils.toJsonString(orderData);
//        OrderData clonedOrder = JsonUtils.fromJson(json, OrderData.class);
        //orderData在每次上报结束之后会将detail数据删除，导致异步请求在subscribe()时detail数据没有上报到平台。这里使用深克隆来解决。
        // 使用 new ArrayList 可实现上面缺失问题

        OrderUpdateData orderUpdateRequest = new OrderUpdateData();
        // orderUpdateRequest.setDetails(!CollectionUtils.isEmpty(orderData.getDetails()) ? new ArrayList<>(orderData.getDetails()) : new ArrayList<>());
        orderUpdateRequest.setDetails(
            orderData.getDetails() == null ? List.of() : orderData.getDetails());
        orderUpdateRequest.setEvseNo(evseNo);
        orderUpdateRequest.setPlugId(plugId);
        orderUpdateRequest.setOrderNo(orderData.getOrderNo());
        return orderUpdateRequest;
    }
}
