package com.cdz360.iot.gw.biz;

import com.cdz360.base.model.charge.vo.ChargePriceItem;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisChargeOrderReadService;
import com.cdz360.data.cache.RedisChargeOrderRwService;
import com.cdz360.iot.gw.north.model.OrderData;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Slf4j
@Service
public class RedisOrderCacheService {

    private static final String REDIS_KEY_ORDER_INFO = "gw:order:";
    private static final String REDIS_KEY_TRANS = "gw:trans:";
    private static final int REDIS_ORDER_UPDATE_TTL_MINUTES = 7 * 24;    // 7天
    private static final int REDIS_ORDER_STOP_TTL_MINUTES = 2;    // 2小时

    @Autowired
    protected StringRedisTemplate redisTemplate;

    @Autowired
    private RedisChargeOrderRwService redisChargeOrderRwService;

    @Autowired
    private RedisChargeOrderReadService redisChargeOrderReadService;

    public Optional<OrderData> getOrder(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            log.info("充电订单号为空");
            return Optional.empty();
        }

        String key = genOrderRedisKey(orderNo);
        String value = this.redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(value)) {
            return Optional.empty();
        }

        OrderData order = JsonUtils.fromJson(value, OrderData.class);
        return Optional.of(order);
    }

    public Optional<String> getOrderNo(Integer transId) {
        if (transId == null) {
            log.info("transId为空");
            return Optional.empty();
        }

        String key = genTransRedisKey(transId);
        String orderNo = this.redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(orderNo)) {
            return Optional.empty();
        }

        return Optional.of(orderNo);
    }

    public void newOrder(OrderData order) {
        if (StringUtils.isBlank(order.getOrderNo())) {
            log.info("充电订单号为空: order = {}", JsonUtils.toJsonString(order));
            return;
        }

        // 保存订单信息
        String key = genOrderRedisKey(order.getOrderNo());
        this.redisTemplate.opsForValue().set(key, JsonUtils.toJsonString(order));
        this.redisTemplate.expire(key, REDIS_ORDER_UPDATE_TTL_MINUTES, TimeUnit.HOURS);

        // 保存transId 和 orderNo 的映射关系
        String transKey = genTransRedisKey(order.getTransId());
        this.redisTemplate.opsForValue().set(transKey, order.getOrderNo());
        this.redisTemplate.expire(transKey, REDIS_ORDER_UPDATE_TTL_MINUTES, TimeUnit.HOURS);

        log.info("新建订单缓存redis: order = {}", JsonUtils.toJsonString(order));
    }

    public void updateOrder(OrderData order) {
        if (StringUtils.isBlank(order.getOrderNo())) {
            log.info("充电订单号为空: order = {}", JsonUtils.toJsonString(order));
            return;
        }

        Optional<OrderData> cacheOrder = this.getOrder(order.getOrderNo());
        Pair<Boolean, OrderData> result = Pair.of(false, null);
        if (cacheOrder.isPresent()) {
            // 整合成新的数据
            result = compareAndUpdateProperties(cacheOrder.get(), order);
        }

        if (result.getLeft()) { // 有更新
            order = result.getRight();
            order.setUpdateTime(new Date());

            String key = genOrderRedisKey(order.getOrderNo());
            // TODO: 2025/3/26 WZFIX 待删除日志
            log.info("写入redis缓存: order = {}", JsonUtils.toJsonString(order));
            this.redisTemplate.opsForValue().set(key, JsonUtils.toJsonString(order));
            this.redisTemplate.expire(key, REDIS_ORDER_UPDATE_TTL_MINUTES, TimeUnit.HOURS);
        }
    }

    public void stopOrder(OrderData order) {
        if (StringUtils.isBlank(order.getOrderNo())) {
            log.info("充电订单号为空: order = {}", JsonUtils.toJsonString(order));
            return;
        }

        // 更新缓存数据
        this.updateOrder(order);

        // 调整缓存时间
        String key = genOrderRedisKey(order.getOrderNo());
        this.redisTemplate.expire(key, REDIS_ORDER_STOP_TTL_MINUTES, TimeUnit.HOURS);
    }

    public static String genOrderRedisKey(String orderNo) {
        return REDIS_KEY_ORDER_INFO + orderNo;
    }

    public static String genTransRedisKey(Integer transId) {
        return REDIS_KEY_TRANS + transId;
    }

    private static Pair<Boolean, OrderData> compareAndUpdateProperties(OrderData cache, OrderData current) {
        // 使用JSON序列化处理，而不是Map转换
        String cacheJson = JsonUtils.toJsonString(cache);
        OrderData mergedOrder = JsonUtils.fromJson(cacheJson, OrderData.class);

        // 使用反射获取current中的非空字段，并设置到mergedOrder中
        boolean changed = false;
        try {
            for (java.lang.reflect.Field field : OrderData.class.getDeclaredFields()) {
                field.setAccessible(true);
                Object currentValue = field.get(current);
                if (currentValue != null) {
                    Object cacheValue = field.get(mergedOrder);
                    // 如果当前值不为空且与缓存值不同，则更新
                    if (!ObjectUtils.nullSafeEquals(currentValue, cacheValue)) {
                        field.set(mergedOrder, currentValue);
                        changed = true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("合并订单数据时出错 orderNo: {}", cache.getOrderNo(), e);
        }

        return Pair.of(changed, mergedOrder);
    }

    public static void main(String[] args) {
        OrderData data = new OrderData();
        data.setOrderNo("123456789");

        ChargePriceVo priceVo = new ChargePriceVo();
        ChargePriceItem item = new ChargePriceItem();
        item.setElecPrice(BigDecimal.TEN);
        item.setServPrice(BigDecimal.ONE);
        priceVo.setItemList(List.of(item));
        data.setPrice(priceVo);

        OrderData data1 = new OrderData();
        data1.setOrderNo("123456789");
        data1.setAmount(BigDecimal.TEN);
        Pair<Boolean, OrderData> result = compareAndUpdateProperties(data, data1);
        log.info("changed = {}", result.getLeft());
        log.info("data = {}", result.getRight());
    }
}
