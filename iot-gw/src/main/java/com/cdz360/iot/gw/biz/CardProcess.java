package com.cdz360.iot.gw.biz;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.biz.ds.CardConfigCache;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.evse.CloudConfig;
import com.cdz360.iot.gw.model.evse.protocol.CardModifyDown;
import com.cdz360.iot.gw.model.evse.protocol.CardQueryDown;
import com.cdz360.iot.gw.model.type.ConstantCollections;
import com.cdz360.iot.gw.model.type.EvseBrand;
import com.cdz360.iot.gw.model.type.EvseCfgResultType;
import com.cdz360.iot.gw.north.model.ConfigInfo;
import com.cdz360.iot.gw.north.model.ConfigModifyResult;
import com.cdz360.iot.gw.north.model.WhiteCard;
import com.cdz360.iot.gw.north.mq.model.ConfigQueryRequest;
import com.cdz360.iot.gw.north.server.ConfigService;
import com.cdz360.iot.gw.south.server.JsonServerImpl;
import com.cdz360.iot.gw.south.server.WhiteCardCache;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import eu.chargetime.ocpp.model.localauthlist.UpdateStatus;
import java.util.List;
import javax.annotation.Nonnull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

@Service
@Scope(value = "singleton")
public class CardProcess {

    private final Logger logger = LoggerFactory.getLogger(CardProcess.class);

    @Autowired
    private JsonServerImpl jsonServer;

    @Autowired
    private ConfigService configService;

    @Autowired
    private CardConfigCache cardConfigCache;

    @Autowired
    private WhiteCardCache whiteCardCache;

    @Autowired
    private EvseRepository evseRepository;

    @Value("${cdz360.timer.cfg-report-timeout}")
    private long cfgReportTimeout;

    public void deliveryCard(@Nonnull CardModifyDown cloudConfig, @Nonnull String cfgVer) {
        String traceId = cloudConfig.getBase().getTid();
        String evseNo = cloudConfig.getBase().getEvseNo();
        boolean success;
        try {
            boolean clearCacheRes = true;
            GwEvseVo evse = evseRepository.getEvse(evseNo);
            if (EvseBrand.RCD.equals(evse.getBrand())) {
                // RCD桩LocalList下发前需要清空Cache
                clearCacheRes = jsonServer.sendClearCacheRequest(traceId, evseNo);
            }
            UpdateStatus updateStatus = jsonServer.sendLocalList(cloudConfig, cfgVer);
            success = clearCacheRes && UpdateStatus.Accepted.equals(updateStatus);
        } catch (Exception ex) {
            logger.error("[{}] 下发紧急卡时发生错误 error: {}", traceId, ex.getMessage(), ex);
            success = false;
        }
        this.reportCardResult(traceId, cloudConfig.getBase().getEvseNo(), cfgVer,
            success ? EvseCfgResultType.SUCCESS : EvseCfgResultType.FAIL,
            success ? ConstantCollections.OK : ConstantCollections.ERROR); // 紧急卡
    }

    public void getCard(String traceId, ConfigQueryRequest request) {
        logger.info("[{}] 下发获取紧急卡指令", traceId);

        if (request == null) {
            logger.error("[{}] 下发获取紧急卡的指令错误 request is null", traceId);
            throw new IllegalArgumentException("下发获取紧急卡的指令错误");
        }

        if (StringUtils.isEmpty(request.getData())) {
            logger.error("[{}] 获取紧急卡时未指定桩号", traceId);
            throw new IllegalArgumentException("获取紧急卡时未指定桩号");
        }
        String evseNo = request.getData();

//        ConfigQueryData configQueryData = new ConfigQueryData();
//        configQueryData.setEvseId(request.getData());
//        configQueryData.setSeqNo(request.getSeq());

        EvseMsgBase base = new EvseMsgBase();
        base.setEvseNo(evseNo)
            .setTid(traceId)
            .setSeq(SeqGeneratorUtil.newIntegerId(evseNo));

        CardQueryDown dMsg = new CardQueryDown(base);
        Integer listVersion = null;
        try {
            listVersion = jsonServer.enquiryLocalListVersion(dMsg);
        } catch (Exception ex) {
            logger.error("[{}] 获取紧急卡时发生错误 error: {}", traceId, ex.getMessage(), ex);
        }

        if (listVersion != null) {
            List<WhiteCard> list = whiteCardCache.getWhiteCards(evseNo, listVersion);
            if (CollectionUtils.isNotEmpty(list)) {
                ConfigInfo info = new ConfigInfo();
                info.setEvseNo(evseNo);
                info.setWhiteCards(list);
                configService.queryConfigInfo(traceId, info);
            }
        }

/*
        boolean result = cardQueryCommander.sendReply(request.getSeq(), dMsg);

        if (result) {
            logger.info("下发获取紧急卡指令完成。traceId: {}, evseId: {}", traceId,
                request.getData());
        } else {
            logger.warn("下发获取紧急卡指令失败。traceId: {}, evseId: {}", traceId,
                request.getData());
        }
        */
    }

    public void reportCardResult(String traceId, String evseId, String version,
        EvseCfgResultType resultType, int whiteCardResult) {

        try {
            logger.info(
                "[{}] 上报紧急卡结果 version: {}, evseId: {}, resultType: {}, subResult: {}",
                traceId, version, evseId, resultType, whiteCardResult);

            if (StringUtils.isEmpty(version)) {
                String json = cardConfigCache.get(ConstantCollections.GW_CFG_CARD, evseId);

                if (StringUtils.isEmpty(json)) {
                    logger.info("[{}} 桩自行上报紧急卡结果失败，定时器已经完成上报。", traceId);
                    return;
                }

                CloudConfig cloudConfig = JsonUtils.fromJson(json, CloudConfig.class);
                version = cloudConfig.getCfgVer();
            }

            if (StringUtils.isEmpty(version)) {
                logger.info("[{}] 配置版本号不存在", traceId);
                throw new IllegalArgumentException("配置版本号不存在");
            }

            ConfigModifyResult request = new ConfigModifyResult();
            request.setCfgVer(version);
            request.setEvseNo(evseId);
            request.setResult(resultType.name());
            request.setWhiteCardsResult(whiteCardResult);

            cardConfigCache.remove(ConstantCollections.GW_CFG_CARD, evseId);
            configService.reportModifyResult(traceId, request);

        } catch (Exception e) {
            logger.error("[{}] 上报桩配置结果时发生错误 error: {}", traceId, e.getMessage(), e);
        }
    }

}
