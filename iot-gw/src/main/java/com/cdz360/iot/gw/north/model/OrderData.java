package com.cdz360.iot.gw.north.model;

import com.cdz360.base.model.base.annotation.Cache;
import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.evse.rcd.RcdBillReqDto;
import com.cdz360.iot.gw.model.type.ChargeType;
import com.cdz360.iot.gw.model.type.OrderStopMode;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.logging.log4j.util.Strings;

@Data
@Accessors(chain = true)
public class OrderData {

    private String seq;//云端返回请求消息中的seq
    private int status;//云端固定返回成功

    // 交易ID
    @Cache
    private Integer transId;

    // 桩编号
    @Cache
    private String evseNo;

    // 序号
    @Cache
    private int idx;

    @Cache
    //订单号. 长度不得超过16位.
    private String orderNo;

    @Cache
    private ChargeOrderStatus orderStatus;

    @Cache
    // 费用抵扣模式
    private ChargeType chargeType;

    @Cache
    // 现金帐户总余额
    // 账户总可用余额, 单位'元'
    private BigDecimal balance;

    @Cache
    //现金帐户可用余额
    //可实时扣费金额, 单位'元'
    private BigDecimal amount;


    @Cache
    //停止方式
    private OrderStopMode stopMode;

    //启动方式
    @Cache
    private Integer startType;

    // 账户: 逻辑卡号或VIN码. 卡号使用BCD编码
    private String account;

    @Cache
    private String accountType;

    // 停充码
    private String stopCode;

    // 车牌号
    private String carNo;

    // 功率限制
    private Integer power;

    @Cache
    private Integer startSoc;

    // SOC限制
    @Cache
    private Integer soc;

    @Cache
    private Long priceCode;

    // 价格信息
    @Cache
    private ChargePriceVo price;

    @Cache
    private LatestOrderFee latestOrderFee;

    @Cache
    private RcdBillReqDto rcdBillReqDto;

    @Cache
    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


    // 是否立即上传心跳数据
    private boolean uploadHb = false;

    // 充电开始时间
    @Cache
    private long startTime;

    //订单停止后完成时间（用来和定时任务时间比较）
    private long stopFinishTime;

    //订单停止后需要删除的标记
    private int needDel;

    // 上一个心跳的序列号
    private Long lastHbSeq = 0L;

    // 充电开始时的电表读数（kWh）
    @Cache
    private BigDecimal startMeter;

    // 当前电表读数（kWh）
    @Cache
    private BigDecimal currMeter;

    @Cache
    private OrderDetail lastOrderDetail;

    // <时段, 时段账单详情> （网关计费）
    @Cache
    private Map<String, OrderDetail> billingDetails;

    //分时段详情,心跳中会更新
    private List<OrderTimeIntervalDetail> details;

    public OrderData() {
    }

    public OrderData(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 保存账单详情（已防重）
     * @param detail
     */
    public void pushBillingDetail(OrderDetail detail) {
        if (billingDetails == null) {
            billingDetails = new HashMap<>();
        }
        String key = Strings.concat(detail.getStartTimeStr(), detail.getStopTimeStr());
        billingDetails.put(key, detail);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

    @Data
    public static class LatestOrderFee {

        /**
         * 充电时长（分钟）
         */
        private Integer chargeTime;

        /**
         * 订单金额（元）
         */
        private BigDecimal orderAmount;

        /**
         * 电费（元）
         */
        private BigDecimal elecAmount;

        /**
         * 服务费（元）
         */
        private BigDecimal servAmount;

    }

}
