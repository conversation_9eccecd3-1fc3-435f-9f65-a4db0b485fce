package com.cdz360.iot.gw.config;

public interface IotGwConstants {

    int SUCCESS = 0;
    int UNKNOWN_ERROR = 9999;
    int SERVICE_ERROR = 2000;

    byte EVSE_FIRST_BYTE = 0x68; // 桩端报文首字节

    /**
     * 公共报文头长度,
     * 0x68 -- 1字节
     * 控制位 -- 1字节
     * 桩编号 -- 6字节
     * 枪口编号 -- 1字节
     * 报文序列号 -- 4字节
     * 时间戳 -- 4字节
     * 数据长度 -- 2字节
     */
    int EVSE_COMMON_HEADER_LENGTH = 19;

    /**
     * 报文签名长度
     */
    int EVSE_SIGN_LNEGTH = 4;

    /**
     * 桩协议公共数据长度
     */
    int EVSE_PROTOCOL_COMMON_LENGTH = EVSE_COMMON_HEADER_LENGTH + EVSE_SIGN_LNEGTH;

    String MQ_EXCHANGE_NAME = "exchangeIotGw";
    String MQ_QUEUE_NAME = "iotGwCmd.gw";

    String PAY_ACCOUNT_TYPE_PREPAY = "PREPAY";


    /**
     * 订单号长度, 16个字节
     */
    int ORDER_NO_BYTES = 16;

    /**
     * 要包含在 MeterValues.req PDU 中的采样测量值
     */
    String METER_VALUES_SAMPLED_DATA = "MeterValuesSampledData";

    String CURRENT_IMPORT = "Current.Import";

    String POWER_ACTIVE_IMPORT = "Power.Active.Import";

    String TEMPERATURE = "Temperature";

    String VOLTAGE = "Voltage";

    String SoC = "SoC";

    String ENERGY_ACTIVE_IMPORT_INTERVAL = "Energy.Active.Import.Interval";

    String WINLINE_CONFIG_KEY_QR_CODE_PREFIX = "QRcode";


}
