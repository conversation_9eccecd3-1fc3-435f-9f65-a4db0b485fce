package com.cdz360.iot.gw.config.ws;

import static eu.chargetime.ocpp.utilities.TraceUtil.shortSession;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.EvseStatusReportService;
import com.cdz360.iot.gw.model.base.SocketInfo;
import com.cdz360.iot.gw.south.server.SocketRepository;
import eu.chargetime.ocpp.AuthenticationException;
import eu.chargetime.ocpp.ServerEvents;
import eu.chargetime.ocpp.model.SessionInformation;
import eu.chargetime.ocpp.utilities.EvseUtil;
import java.util.UUID;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Slf4j
public class ServerEventConfig {

    @Autowired
    private SocketRepository socketRepository;

    @Autowired
    private EvseStatusReportService evseStatusReportService;

    @Bean
    public ServerEvents createServerCoreImpl() {
        return getNewServerEventsImpl();
    }

    private ServerEvents getNewServerEventsImpl() {
        return new ServerEvents() {

            @Override
            public void authenticateSession(SessionInformation information, String username,
                String password) throws AuthenticationException {
                String evseNo = EvseUtil.getEvseNo(information.getIdentifier());
                if (!evseStatusReportService.authenticateSessionProcess(
                    information.getSocketAddress(), evseNo, password)) {
                    throw new AuthenticationException(401, "Invalid username or password");
                }
            }

            @Override
            public void newSession(UUID sessionIndex, SessionInformation information) {
                // sessionIndex is used to send messages.
                String socketAddr = information.getSocketAddress();
                String shortSession = shortSession(sessionIndex);
                log.info("[{} {}] newSession. information: {}", socketAddr, shortSession,
                    JsonUtils.toJsonString(information));
                String evseNo = EvseUtil.getEvseNo(information.getIdentifier());

                SocketInfo info = new SocketInfo();
                info.setSocketAddr(socketAddr);
                info.setSessionIndex(sessionIndex);
                info.setEvseNo(evseNo);
                socketRepository.put(socketAddr, info);

                /*
                EvseMsgBase base = new EvseMsgBase(sessionIndex.toString(), socketAddr);
                base.setCmdCode(EvseMessageType.EVSE_REGISTER)
                    .setEvseNo(evseNo);
                BaseEvseMsgUp request = new BaseEvseMsgUp(base);
                evseProcessor.preprocess(request);
                */
            }

            @Override
            public void lostSession(UUID sessionIndex, String socketAddr) {
                log.info("[{} {}] Lost session.", shortSession(sessionIndex), socketAddr);

                //桩断线后实时上报桩/枪状态 & 更新本地桩缓存的状态
                evseStatusReportService.reportWhenTimeout(socketAddr);
            }
        };
    }
}
