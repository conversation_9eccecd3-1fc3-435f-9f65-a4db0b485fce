package com.cdz360.iot.gw.north.mq.consumer;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.UpgradeProcess;
import com.cdz360.iot.gw.north.mq.model.MqEvseUpgradeMsg;
import com.cdz360.iot.gw.north.mq.model.MqMsgBase;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UpgradeConsumer2 implements MqttConsumer {

    @Autowired
    private UpgradeProcess upgradeProcess;

    @Override
    public Integer method() {
        // TODO: 2025/5/27 WZFIX 待修正code
        return IotGwCmdType2.CE_UPGRADE.getCode() + 999;
    }

    @Override
    public void consume(String traceId, JsonNode message) {
        MqMsgBase<MqEvseUpgradeMsg> msg = JsonUtils.fromJson(message, new TypeReference<MqMsgBase<MqEvseUpgradeMsg>>() {
        });
        upgradeProcess.upgradeEvse2(traceId, msg);
    }
}
